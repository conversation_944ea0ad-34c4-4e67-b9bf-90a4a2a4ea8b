# 课程持久化功能测试指南

## 功能概述

现在已经实现了完整的课程内容持久化系统，解决了课程内容重复生成的问题。

## 新增功能

### 1. 后端改进
- ✅ 集成记忆管理器
- ✅ 本地文件存储系统
- ✅ 课程存在检查API
- ✅ 自动缓存机制

### 2. 前端改进
- ✅ localStorage缓存
- ✅ 多层缓存检查
- ✅ 内容来源显示
- ✅ 智能加载机制

### 3. 本地存储
- ✅ courses/ 目录自动创建
- ✅ JSON文件自动保存
- ✅ 文件命名规则
- ✅ 版本管理

## 测试步骤

### 步骤1：首次访问
1. 启动项目：`npm run dev` 和 `cd backend && python src/main.py`
2. 访问首页，输入主题（如："Python编程基础"）
3. 观察：
   - 显示"正在为您生成全新的课程大纲..."
   - 页面左侧显示"🆕 新生成的内容"
   - 检查 `backend/courses/` 目录，应该有新文件

### 步骤2：重新访问
1. 回到首页，输入相同主题
2. 观察：
   - 立即加载，无需等待
   - 页面左侧显示"📱 从本地缓存加载"
   - 内容与之前完全一致

### 步骤3：章节内容测试
1. 点击不同章节
2. 首次点击：从API加载并缓存
3. 再次点击：从localStorage立即加载

### 步骤4：跨浏览器会话测试
1. 关闭浏览器
2. 重新打开并访问相同主题
3. 应该从localStorage加载

### 步骤5：服务器重启测试
1. 重启后端服务
2. 访问相同主题
3. 应该从本地文件加载（显示"📄 从文件加载"）

## 文件结构检查

生成课程后，检查以下文件：

```
backend/courses/
├── README.md
├── .gitignore
├── course_[hash].json          # 课程大纲
└── section_[hash]_1_1.json     # 章节内容
```

## 预期行为

1. **首次生成**：API调用 → 记忆存储 → 本地文件 → localStorage
2. **再次访问**：localStorage → 本地文件 → 记忆 → API（按优先级）
3. **内容固定**：一旦生成，内容不会改变
4. **性能提升**：缓存加载速度极快

## 故障排除

### 如果内容没有缓存
1. 检查浏览器控制台错误
2. 检查后端logs
3. 确认courses目录权限
4. 检查localStorage是否被禁用

### 如果想重新生成内容
1. 清除localStorage：`localStorage.clear()`
2. 删除对应的课程文件
3. 重新访问主题

## API端点

新增的API端点：
- `GET /api/course/exists/{topic}` - 检查课程是否存在
- `GET /api/course/list` - 获取所有课程列表
- `GET /api/course/content/{section_id}?topic={topic}` - 获取章节内容（支持缓存）

## 技术特性

- **多层缓存**：localStorage → 本地文件 → 记忆管理器 → API
- **自动持久化**：生成即保存，无需手动操作
- **智能加载**：优先使用缓存，提升用户体验
- **版本管理**：每个文件包含时间戳
- **唯一标识**：基于主题hash的文件命名

现在课程内容将永久保存，用户再次访问时不会重复生成！ 