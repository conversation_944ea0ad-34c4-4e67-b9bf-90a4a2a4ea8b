#!/usr/bin/env python3
"""
更耐心的前端集成测试 - 专门用于测试Ollama本地模型
"""

import asyncio
import websockets
import json
import time
from datetime import datetime


async def test_single_scenario_with_patience():
    """单场景测试，更长的超时时间"""
    
    print("🔧 耐心版前端集成测试 - 适配Ollama本地模型")
    print("=" * 60)
    
    # 用户背景信息
    user_background = {
        "age": "初出茅庐(3~5年级)",
        "learningGoal": "系统学习 - 全面掌握知识体系", 
        "timePreference": "🗓️ 几周的学习计划",
        "knowledgeLevel": "初步抽象思维形成",
        "targetAudience": "初出茅庐(3~5年级)"
    }
    
    user_topic = "我想学习小学数学中的分数知识"
    
    print("📝 用户背景信息:")
    for key, value in user_background.items():
        print(f"   {key}: {value}")
    print(f"\n📋 用户主题: {user_topic}")
    
    # 格式化消息
    formatted_message = f"""{user_topic}

## 用户背景信息
年龄/年级: {user_background['age']}
学习目标: {user_background['learningGoal']}
时间偏好: {user_background['timePreference']}
知识水平: {user_background.get('knowledgeLevel', '')}
目标受众: {user_background.get('targetAudience', '')}

## Agent处理指令
请所有后续Agent (CoursePlanner, ContentGenerator, Monitor, Verifier等) 在处理时考虑以上背景信息：
1. 根据年龄/年级调整内容难度和教学方法
2. 对齐学习目标，确保课程设计符合用户期望
3. 考虑时间约束，合理安排学习进度和内容密度
4. 在内容验证和监督过程中应用这些背景信息作为评估标准"""

    # WebSocket测试
    client_id = f"patient_test_{int(time.time())}"
    websocket_url = f"ws://localhost:8000/api/ws/chat/{client_id}"
    
    print(f"\n🔌 连接WebSocket: {websocket_url}")
    
    try:
        async with websockets.connect(websocket_url) as websocket:
            print("✅ WebSocket连接成功")
            
            # 发送消息
            message_data = {
                "content": formatted_message,
                "sender": "user"
            }
            
            print("📤 发送消息到后端...")
            await websocket.send(json.dumps(message_data))
            
            print("⏳ 等待Agent回复... (最多5分钟，适配本地模型处理时间)")
            
            # 增加超时时间到5分钟，适配本地大模型
            try:
                response = await asyncio.wait_for(websocket.recv(), timeout=1000.0)
                response_data = json.loads(response)
                
                print("🎉 收到Agent回复!")
                print(f"📨 回复发送者: {response_data.get('sender', 'unknown')}")
                print(f"📨 回复长度: {len(response_data.get('content', ''))}")
                
                # 检查回复内容
                content = response_data.get('content', '').lower()
                
                background_keywords = [
                    "小学", "3年级", "4年级", "5年级", 
                    "分数", "系统学习", "几周",
                    "初步抽象思维", "初出茅庐"
                ]
                
                found_keywords = [kw for kw in background_keywords if kw in content]
                
                print(f"\n🔍 背景信息关键词检测:")
                print(f"   找到的关键词: {found_keywords}")
                print(f"   关键词匹配率: {len(found_keywords)}/{len(background_keywords)} ({len(found_keywords)/len(background_keywords)*100:.1f}%)")
                
                if len(found_keywords) >= 3:
                    print("✅ Agent成功使用了用户背景信息!")
                else:
                    print("⚠️ Agent可能没有充分使用用户背景信息")
                
                # 显示完整回复内容
                print(f"\n📄 完整回复内容:")
                print("=" * 60)
                print(response_data.get('content', ''))
                print("=" * 60)
                
                return True
                
            except asyncio.TimeoutError:
                print("❌ 等待回复超时 (5分钟)")
                print("   这可能是因为本地模型处理时间较长，或者模型没有正确响应")
                return False
                
    except Exception as e:
        print(f"❌ WebSocket连接失败: {e}")
        return False


async def main():
    """主函数"""
    
    print("🎯 开始耐心版前端集成测试")
    
    try:
        success = await test_single_scenario_with_patience()
        
        if success:
            print("\n🎉 前端集成测试成功!")
            print("✅ 用户背景信息成功从前端传递到后端并被Agent使用")
        else:
            print("\n❌ 前端集成测试失败")
            print("⚠️ 需要检查后端Agent处理逻辑或模型响应时间")
            
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
