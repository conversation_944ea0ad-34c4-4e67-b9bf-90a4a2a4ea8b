#!/bin/bash

# 课程选择对话框修复验证脚本
# 这个脚本会验证修复是否成功

echo "🛠️  课程选择对话框修复验证"
echo "=================================="

# 检查服务状态
echo "📋 1. 检查服务状态..."

# 检查前端服务
if curl -s http://localhost:8080 > /dev/null; then
    echo "✅ 前端服务 (Vite) 运行正常 - http://localhost:8080"
else
    echo "❌ 前端服务未运行，请启动 Vite 开发服务器"
    exit 1
fi

# 检查后端服务
if curl -s http://localhost:8000/health > /dev/null; then
    echo "✅ 后端服务 (FastAPI) 运行正常 - http://localhost:8000"
else
    echo "❌ 后端服务未运行，请启动 FastAPI 服务器"
    exit 1
fi

echo ""
echo "🔧 2. 验证修复的代码更改..."

# 检查关键修复是否存在
if grep -q "hasProcessedSubmission" src/pages/CoursePlanning.tsx; then
    echo "✅ hasProcessedSubmission 状态已添加"
else
    echo "❌ hasProcessedSubmission 状态未找到"
    exit 1
fi

if grep -q "setHasProcessedSubmission(true)" src/pages/CoursePlanning.tsx; then
    echo "✅ hasProcessedSubmission 设置逻辑已添加"
else
    echo "❌ hasProcessedSubmission 设置逻辑未找到"
    exit 1
fi

if grep -q "!hasProcessedSubmission" src/pages/CoursePlanning.tsx; then
    echo "✅ hasProcessedSubmission 检查逻辑已添加"
else
    echo "❌ hasProcessedSubmission 检查逻辑未找到"
    exit 1
fi

echo ""
echo "🧪 3. 模拟用户流程测试..."

# 创建测试数据
TEST_TOPIC="Python编程基础自动化测试"
TEST_PROMPT="{\"topic\":\"$TEST_TOPIC\",\"age\":\"大学生\",\"learningGoal\":\"掌握Python基础语法\",\"timePreference\":\"4-6小时\"}"

echo "测试主题: $TEST_TOPIC"

# 测试课程生成API
echo "测试课程生成 API..."
COURSE_RESPONSE=$(curl -s -X POST http://localhost:8000/api/course/outline \
  -H "Content-Type: application/json" \
  -d "$TEST_PROMPT")

if echo "$COURSE_RESPONSE" | grep -q "title\|course_title"; then
    echo "✅ 课程生成 API 正常工作"
    COURSE_TITLE=$(echo "$COURSE_RESPONSE" | grep -o '"title":"[^"]*"' | head -1 | cut -d'"' -f4)
    echo "   生成的课程标题: $COURSE_TITLE"
else
    echo "❌ 课程生成 API 返回异常"
    echo "响应: $COURSE_RESPONSE"
fi

echo ""
echo "📱 4. 手动测试指引..."
echo "请在浏览器中执行以下步骤验证修复："
echo ""
echo "步骤 1: 打开首页"
echo "       访问: http://localhost:8080"
echo ""
echo "步骤 2: 输入学习主题"
echo "       主题: $TEST_TOPIC"
echo "       填写背景信息"
echo ""
echo "步骤 3: 点击'生成课程'按钮"
echo "       应该跳转到 /course-planning"
echo ""
echo "步骤 4: 在课程选择对话框中选择'创建新课程'"
echo "       ✅ 验证点: 页面应该停留在课程规划页面"
echo "       ❌ 修复前: 页面会错误跳转回首页"
echo ""
echo "步骤 5: 等待课程生成完成"
echo "       应该看到课程大纲显示"
echo ""

echo "🔍 5. 调试信息..."
echo "如果遇到问题，请检查浏览器控制台中的以下日志："
echo "  - '=== initializePage useEffect triggered ==="
echo "  - '=== Showing course dialog (from submission) ==="
echo "  - '=== handleCreateNewCourse started ==="
echo "  - '=== Submission already processed, skipping initialization ==="
echo ""

echo "📊 6. 关键修复验证..."
echo "修复的核心逻辑:"
echo "  1. 用户提交后: isFromSubmission=true, hasProcessedSubmission=false"
echo "  2. 显示对话框: setHasProcessedSubmission(true)"
echo "  3. 用户选择后: hasProcessedSubmission=true"
echo "  4. useEffect再次触发: 因为 hasProcessedSubmission=true，跳过初始化"
echo "  5. 结果: 页面停留，不会跳转回首页"
echo ""

echo "✅ 验证脚本完成！"
echo "请在浏览器中按照上述步骤手动测试修复效果。"
echo ""
echo "🌐 快速链接:"
echo "  首页: http://localhost:8080"
echo "  验证页面: file://$(pwd)/course-selection-fix-verification.html"
