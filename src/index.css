@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=Playfair+Display:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 210 40% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 246 80% 60%;
    --primary-foreground: 210 40% 98%;

    --secondary: 244 76% 59%;
    --secondary-foreground: 210 40% 98%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 262 83% 63%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 246 80% 60%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 246 80% 60%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 246 80% 60%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 246 80% 60%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 262 83% 63%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 246 80% 60%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 246 80% 60%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-display;
  }
}

.shimmer {
  @apply relative overflow-hidden before:absolute before:inset-0 before:-translate-x-full before:animate-shimmer before:bg-gradient-to-r before:from-transparent before:via-white/20 before:to-transparent;
}

.gradient-bg {
  background: linear-gradient(90deg, #6366F1, #4F46E5, #8B5CF6, #6366F1);
  background-size: 300% 100%;
}

.markdown-content {
  @apply prose prose-indigo max-w-none lg:prose-lg;
}

.markdown-content pre {
  @apply bg-gray-100 p-4 rounded-md overflow-x-auto;
}

.markdown-content code {
  @apply bg-gray-100 px-1 py-0.5 rounded;
}

.chat-bubble {
  @apply relative max-w-[80%] rounded-lg px-4 py-2 shadow-sm;
}

.chat-bubble-user {
  @apply bg-primary text-white rounded-tr-none;
}

.chat-bubble-agent {
  @apply bg-white text-gray-800 rounded-tl-none;
}

/* 毛玻璃效果 */
.backdrop-blur-md {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.65);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.05);
}

/* 增强的毛玻璃效果 */
.glass-modal {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(20px);
  -webkit-backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 
    0 25px 50px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(255, 255, 255, 0.2);
}

/* 模态框动画 */
.modal-overlay {
  animation: modal-fade-in 0.3s ease-out;
}

.modal-content {
  animation: modal-slide-up 0.3s ease-out;
}

@keyframes modal-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes modal-slide-up {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 选项按钮悬停效果 */
.option-button {
  transition: all 0.2s ease;
  backdrop-filter: blur(5px);
  -webkit-backdrop-filter: blur(5px);
}

.option-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.rainbow-flow-container {
  display: none;
}

.rainbow-flow-line {
  display: none;
}

/* 全新的曲线流动彩虹背景 */
.curved-lines-bg {
  position: relative;
  overflow: hidden;
}

.curved-lines-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 0;
  overflow: hidden;
}

.curved-line {
  position: absolute;
  height: 1px;
  width: 100%;
  opacity: 0.4;
  transform-origin: center;
}

/* 创建多条曲线，每条都有不同的路径和颜色 */
.curved-line:nth-child(1) {
  top: 15%;
  background: none;
}

.curved-line:nth-child(1)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #33C3F0 20%, #0EA5E9 40%, #8B5CF6 70%, #6366F1 90%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-1 20s ease-in-out infinite alternate;
}

.curved-line:nth-child(2) {
  top: 25%;
  background: none;
}

.curved-line:nth-child(2)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #0EA5E9 10%, #33C3F0 30%, #34D399 70%, #65C366 90%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-2 22s ease-in-out infinite alternate-reverse;
}

.curved-line:nth-child(3) {
  top: 35%;
  background: none;
}

.curved-line:nth-child(3)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #8B5CF6 10%, #6366F1 30%, #0EA5E9 70%, #33C3F0 90%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-3 25s ease-in-out infinite alternate;
}

.curved-line:nth-child(4) {
  top: 45%;
  background: none;
}

.curved-line:nth-child(4)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #65C366 10%, #34D399 30%, #33C3F0 70%, #0EA5E9 90%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-4 18s ease-in-out infinite alternate-reverse;
}

.curved-line:nth-child(5) {
  top: 55%;
  background: none;
}

.curved-line:nth-child(5)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #34D399 10%, #65C366 40%, #FEF7CD 60%, #33C3F0 90%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-5 24s ease-in-out infinite alternate;
}

.curved-line:nth-child(6) {
  top: 65%;
  background: none;
}

.curved-line:nth-child(6)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #0EA5E9 15%, #33C3F0 40%, #34D399 60%, #65C366 85%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-6 20s ease-in-out infinite alternate-reverse;
}

.curved-line:nth-child(7) {
  top: 75%;
  background: none;
}

.curved-line:nth-child(7)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #8B5CF6 20%, #6366F1 40%, #0EA5E9 70%, #33C3F0 100%);
  filter: blur(0.5px);
  animation: curve-flow-7 28s ease-in-out infinite alternate;
}

.curved-line:nth-child(8) {
  top: 85%;
  background: none;
}

.curved-line:nth-child(8)::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, #33C3F0 20%, #0EA5E9 40%, #8B5CF6 60%, #6366F1 80%, transparent 100%);
  filter: blur(0.5px);
  animation: curve-flow-8 26s ease-in-out infinite alternate-reverse;
}

@keyframes curve-flow-1 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,5 40,-10 60,0 C80,10 100,0 120,-5 C140,-10 160,10 180,0 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(20px) scaleY(1.1);
    clip-path: path('M0,0 C20,-5 40,10 60,0 C80,-10 100,5 120,10 C140,15 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,10 40,0 60,5 C80,10 100,-10 120,0 C140,10 160,5 180,-5 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-2 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,10 40,0 60,5 C80,10 100,-5 120,0 C140,5 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(-15px) scaleY(1.05);
    clip-path: path('M0,0 C20,-5 40,5 60,-5 C80,-10 100,5 120,0 C140,-5 160,10 180,0 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-10 40,0 60,-5 C80,-10 100,5 120,0 C140,-5 160,10 180,5 C200,0 220,-5 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-3 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-5 40,10 60,-5 C80,0 100,5 120,-5 C140,-10 160,5 180,0 C200,-5 220,10 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(25px) scaleY(1.08);
    clip-path: path('M0,0 C20,10 40,-5 60,5 C80,10 100,-5 120,0 C140,5 160,0 180,-5 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,5 40,-10 60,0 C80,5 100,-5 120,0 C140,5 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-4 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,5 40,-5 60,5 C80,0 100,-5 120,5 C140,10 160,-5 180,0 C200,5 220,0 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(-20px) scaleY(1.12);
    clip-path: path('M0,0 C20,-5 40,10 60,-5 C80,-10 100,5 120,0 C140,-5 160,10 180,0 C200,-5 220,10 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,10 40,-5 60,0 C80,5 100,10 120,-5 C140,-10 160,5 180,0 C200,-5 220,10 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-5 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-10 40,5 60,0 C80,-5 100,10 120,5 C140,0 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(15px) scaleY(1.1);
    clip-path: path('M0,0 C20,5 40,0 60,-5 C80,-10 100,5 120,10 C140,15 160,-5 180,0 C200,5 220,-10 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,10 40,-5 60,0 C80,5 100,-5 120,0 C140,5 160,10 180,-5 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-6 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,5 40,-5 60,5 C80,10 100,-5 120,0 C140,5 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(-25px) scaleY(1.15);
    clip-path: path('M0,0 C20,-5 40,10 60,-5 C80,-10 100,5 120,0 C140,-5 160,10 180,5 C200,0 220,-5 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-10 40,0 60,-5 C80,0 100,5 120,-5 C140,-10 160,5 180,0 C200,-5 220,10 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-7 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,10 40,-5 60,0 C80,5 100,-5 120,0 C140,5 160,0 180,-5 C200,-10 220,5 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(20px) scaleY(1.08);
    clip-path: path('M0,0 C20,-5 40,10 60,5 C80,0 100,-5 120,0 C140,5 160,-5 180,0 C200,5 220,0 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-5 40,10 60,-5 C80,-10 100,5 120,10 C140,15 160,-5 180,0 C200,5 220,-5 240,0 L240,1 L0,1 Z');
  }
}

@keyframes curve-flow-8 {
  0% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,-10 40,5 60,0 C80,-5 100,10 120,0 C140,-10 160,5 180,0 C200,-5 220,10 240,0 L240,1 L0,1 Z');
  }
  50% {
    transform: translateY(-15px) scaleY(1.12);
    clip-path: path('M0,0 C20,10 40,-5 60,0 C80,5 100,0 120,-5 C140,-10 160,5 180,10 C200,15 220,-5 240,0 L240,1 L0,1 Z');
  }
  100% {
    transform: translateY(0) scaleY(1);
    clip-path: path('M0,0 C20,5 40,-10 60,0 C80,10 100,-5 120,0 C140,5 160,-5 180,0 C200,5 220,-10 240,0 L240,1 L0,1 Z');
  }
}

/* 更新其他样式 */
@keyframes rainbow-wave {
  0% {
    background-position: 0% 50%;
  }
  100% {
    background-position: 100% 50%;
  }
}

@keyframes float-line {
  0%, 100% {
    transform: translateY(-5px) rotate(0.2deg);
  }
  50% {
    transform: translateY(5px) rotate(-0.2deg);
  }
}
