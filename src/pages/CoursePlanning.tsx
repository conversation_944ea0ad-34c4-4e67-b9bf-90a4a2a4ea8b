// filepath: /Users/<USER>/Documents/quest-agent-verse/src/pages/CoursePlanning.tsx
import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import Navbar from '@/components/Navbar';
import MarkdownRenderer from '@/components/MarkdownRenderer';
import LoadingPlaceholder from '@/components/LoadingPlaceholder';
import { useChat } from '@/contexts/ChatContext';
import { getCourseOutline, getCourseContent, checkCourseExists } from '@/services/api';
import { toast } from '@/components/ui/sonner';
import { Separator } from '@/components/ui/separator';
import CourseSelectionDialog from '@/components/course/CourseSelectionDialog';
import {
  analyzeCourseCompleteness,
  shouldRegenerateOutline,
  needsContentGeneration,
  getMissingSections,
  generateAllMissingContent,
  checkSectionCached as utilCheckSectionCached,
  type CourseCompleteness
} from '@/utils/courseCompletenessChecker';

interface Section {
  id: string;
  title: string;
  content_type?: string;
  activity_suggestion?: string;
}

interface BloomTaxonomyObjectives {
  remember: string[];
  understand: string[];
  apply: string[];
  analyze: string[];
  evaluate: string[];
  create: string[];
}

interface TeachingResources {
  interactive_activities: string[];
  media_types: string[];
  assessment_methods: string[];
}

interface ContentDesignGuidance {
  content_structure: string;
  difficulty_level: string;
  examples_needed: string;
  practice_activities: string;
}

interface BackgroundAnalysis {
  target_age: string;
  knowledge_level: string;
  learning_context: string;
}

interface CurriculumAlignment {
  standards_used: string;
  alignment_overview: string;
}

interface Chapter {
  id: string;
  title: string;
  description?: string;
  estimated_duration?: string;
  bloom_focus?: string[];
  learning_objectives?: string[];
  key_concepts?: string[];
  teaching_resources?: TeachingResources;
  content_design_guidance?: ContentDesignGuidance;
  curriculum_alignment?: string;
  sections: Section[];
}

interface CourseOutline {
  course_title?: string;
  course_description?: string;
  background_analysis?: BackgroundAnalysis;
  bloom_taxonomy_objectives?: BloomTaxonomyObjectives;
  curriculum_alignment?: CurriculumAlignment;
  chapters: Chapter[];
  // Legacy support for existing API responses
  title?: string;
}

interface CourseContent {
  title: string;
  mainContent: string;
  keyPoints: string[];
  images: { url: string; caption: string }[];
  curriculumAlignment: string[];
}

const CoursePlanning = () => {
  const navigate = useNavigate();
  const {
    initialPrompt,
    setHasCourseGenerated,
    isFromSubmission,
    setIsFromSubmission
  } = useChat();
  const [outline, setOutline] = useState<CourseOutline | null>(null);
  const [content, setContent] = useState<CourseContent | null>(null);
  const [selectedSection, setSelectedSection] = useState<string | null>(null);
  const [isLoadingOutline, setIsLoadingOutline] = useState(false);
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  const [courseSource, setCourseSource] = useState<string>(''); // 'cache', 'api', 'localStorage'

  // 新增状态管理
  const [showCourseDialog, setShowCourseDialog] = useState(false);
  const [courseCompleteness, setCourseCompleteness] = useState<CourseCompleteness | null>(null);
  const [isGeneratingContent, setIsGeneratingContent] = useState(false);
  const [contentGenerationProgress, setContentGenerationProgress] = useState<{[key: string]: 'loading' | 'success' | 'error'}>({});
  const [isCreatingCourse, setIsCreatingCourse] = useState(false); // 防止在课程创建过程中导航
  const [hasProcessedSubmission, setHasProcessedSubmission] = useState(false); // 标记是否已处理过提交

  // 使用 useRef 保护 initialPrompt，防止在组件重新渲染时丢失
  const stablePromptRef = useRef<string>('');

  // 当 initialPrompt 变化时，更新 ref
  useEffect(() => {
    if (initialPrompt) {
      stablePromptRef.current = initialPrompt;
      console.log('=== Stable prompt updated ===', initialPrompt);
    }
  }, [initialPrompt]);

  // 调试用：监控页面导航
  useEffect(() => {
    console.log('=== CoursePlanning component mounted/updated ===');
    console.log('Current pathname:', window.location.pathname);
    console.log('initialPrompt:', initialPrompt);
    console.log('isFromSubmission:', isFromSubmission);

    const handlePopState = () => {
      console.log('=== PopState event detected ===');
      console.log('New pathname:', window.location.pathname);
    };

    window.addEventListener('popstate', handlePopState);

    return () => {
      window.removeEventListener('popstate', handlePopState);
    };
  }, [initialPrompt, isFromSubmission]);

  // 生成课程缓存key
  const getCourseStorageKey = (topic: string) => `course_outline_${topic.replace(/\s+/g, '_')}`;
  const getSectionStorageKey = (topic: string, sectionId: string) =>
    `course_section_${topic.replace(/\s+/g, '_')}_${sectionId}`;

  // 保存课程到localStorage
  const saveCourseToStorage = (topic: string, courseData: CourseOutline) => {
    try {
      const storageKey = getCourseStorageKey(topic);
      const dataToStore = {
        ...courseData,
        cachedAt: new Date().toISOString(),
        topic: topic
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToStore));
      console.log('Course saved to localStorage:', storageKey);
    } catch (error) {
      console.error('Error saving course to localStorage:', error);
    }
  };

  // 从localStorage加载课程
  const loadCourseFromStorage = (topic: string): CourseOutline | null => {
    try {
      const storageKey = getCourseStorageKey(topic);
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        console.log('Course loaded from localStorage:', storageKey);
        return parsedData;
      }
    } catch (error) {
      console.error('Error loading course from localStorage:', error);
    }
    return null;
  };

  // 保存章节内容到localStorage
  const saveSectionToStorage = (topic: string, sectionId: string, sectionData: CourseContent) => {
    try {
      const storageKey = getSectionStorageKey(topic, sectionId);
      const dataToStore = {
        ...sectionData,
        cachedAt: new Date().toISOString(),
        topic: topic,
        sectionId: sectionId
      };
      localStorage.setItem(storageKey, JSON.stringify(dataToStore));
      console.log('Section saved to localStorage:', storageKey);
    } catch (error) {
      console.error('Error saving section to localStorage:', error);
    }
  };

  // 从localStorage加载章节内容
  const loadSectionFromStorage = (topic: string, sectionId: string): CourseContent | null => {
    try {
      const storageKey = getSectionStorageKey(topic, sectionId);
      const storedData = localStorage.getItem(storageKey);
      if (storedData) {
        const parsedData = JSON.parse(storedData);
        console.log('Section loaded from localStorage:', storageKey);
        return parsedData;
      }
    } catch (error) {
      console.error('Error loading section from localStorage:', error);
    }
    return null;
  };

  // 检查章节是否有缓存内容
  const checkSectionCached = (sectionId: string): boolean => {
    if (!initialPrompt) return false;
    return utilCheckSectionCached(initialPrompt, sectionId);
  };

  // 初始化页面，只有在用户从首页提交主题后才显示课程选择对话框
  useEffect(() => {
    const initializePage = async () => {
      console.log('=== initializePage useEffect triggered ===');
      console.log('initialPrompt:', initialPrompt);
      console.log('stablePromptRef.current:', stablePromptRef.current);
      console.log('isFromSubmission:', isFromSubmission);
      console.log('hasProcessedSubmission:', hasProcessedSubmission);
      console.log('isCreatingCourse:', isCreatingCourse);

      // 使用稳定的prompt引用，防止在异步操作中丢失
      const currentPrompt = initialPrompt || stablePromptRef.current;

      // 如果正在创建课程，不要触发任何逻辑，防止干扰课程生成
      if (isCreatingCourse) {
        console.log('=== Course creation in progress, skipping all initialization logic ===');
        return;
      }

      // 如果已经处理过提交且不是从新提交来的，跳过所有逻辑
      if (hasProcessedSubmission && !isFromSubmission) {
        console.log('=== Submission already processed and not from new submission, skipping initialization ===');
        return;
      }

      if (!currentPrompt) {
        console.log('=== No prompt available, navigating to home ===');
        // If there's no initial prompt, navigate back to the landing page
        toast.error('请先输入您想学习的内容');
        navigate('/');
        return;
      }

      // 只有当用户是从首页提交主题后进入的页面，才显示课程选择对话框
      if (isFromSubmission && !hasProcessedSubmission) {
        console.log('=== Showing course dialog (from submission) ===');
        setShowCourseDialog(true);
        setHasProcessedSubmission(true);
        // 重置标识，避免在其他导航中重复显示对话框
        setIsFromSubmission(false);
      } else if (!hasProcessedSubmission && !isFromSubmission) {
        // 只有在直接访问且没有处理过提交的情况下才尝试加载缓存
        console.log('=== Loading from cache (direct access) ===');
        const cachedCourse = loadCourseFromStorage(currentPrompt);
        if (cachedCourse) {
          console.log('Found cached course, loading directly');
          setOutline(cachedCourse);
          setCourseSource('localStorage');
          setHasCourseGenerated(true);

          // 选择第一个章节
          if (cachedCourse.chapters?.length > 0 && cachedCourse.chapters[0].sections?.length > 0) {
            setSelectedSection(cachedCourse.chapters[0].sections[0].id);
          }

          toast.success('已加载缓存的课程');
        } else {
          // 如果没有缓存，提示用户重新生成
          toast.error('未找到课程缓存，请返回首页重新生成课程');
          setTimeout(() => navigate('/'), 2000);
        }
      } else {
        console.log('=== Other conditions, skipping initialization ===');
      }
    };

    initializePage();
  }, [initialPrompt, navigate, isFromSubmission, setIsFromSubmission, hasProcessedSubmission, isCreatingCourse]);

  // 处理选择现有课程
  const handleSelectExistingCourse = async (course: any) => {
    try {
      setIsLoadingOutline(true);
      setShowCourseDialog(false);
      setHasProcessedSubmission(true); // 标记已处理提交

      console.log('Selected existing course:', course);

      // 分析课程完整性
      const completeness = analyzeCourseCompleteness(initialPrompt!, course.course_data);
      setCourseCompleteness(completeness);

      if (completeness.status === 'complete') {
        // 完整课程 - 直接加载
        console.log('Loading complete course from cache');
        setOutline(course.course_data);
        setCourseSource('cache');
        setHasCourseGenerated(true);

        // 选择第一个章节
        if (course.course_data.chapters?.length > 0 && course.course_data.chapters[0].sections?.length > 0) {
          setSelectedSection(course.course_data.chapters[0].sections[0].id);
        }

        toast.success('已加载完整课程');

      } else if (completeness.status === 'incomplete_content') {
        // 大纲完整但内容不完整 - 加载大纲到左面板，显示占位符在右面板
        console.log('Course has complete outline but missing content, loading outline and generating content');
        setOutline(course.course_data);
        setCourseSource('cache');
        setHasCourseGenerated(true);

        // 选择第一个章节（会触发内容生成到右面板）
        if (course.course_data.chapters?.length > 0 && course.course_data.chapters[0].sections?.length > 0) {
          setSelectedSection(course.course_data.chapters[0].sections[0].id);
        }

        toast.info('课程大纲已加载到左侧，正在生成章节内容到右侧...');

        // 后台生成缺失内容
        generateMissingContent(completeness.missing_sections);

      } else if (completeness.status === 'incomplete_outline') {
        // 大纲不完整 - 重新生成课程
        console.log('Course has incomplete outline, regenerating course');
        toast.info('课程大纲不完整，正在重新生成课程...');
        await handleCreateNewCourse();

      } else {
        // 空课程 - 重新生成
        console.log('Empty course, regenerating');
        toast.info('课程为空，正在生成新课程...');
        await handleCreateNewCourse();
      }

    } catch (error) {
      console.error('Error handling existing course selection:', error);
      toast.error('加载选中课程失败，请重试');
    } finally {
      setIsLoadingOutline(false);
    }
  };

  // 处理创建新课程
  const handleCreateNewCourse = async () => {
    // 使用稳定的prompt引用
    const currentPrompt = initialPrompt || stablePromptRef.current;

    try {
      console.log('=== handleCreateNewCourse started ===');
      console.log('Current location:', window.location.pathname);
      console.log('initialPrompt at start:', initialPrompt);
      console.log('stablePromptRef.current:', stablePromptRef.current);

      // 立即设置创建状态和处理标记，防止任何干扰
      setIsCreatingCourse(true);
      setHasProcessedSubmission(true);
      setShowCourseDialog(false);
      setIsLoadingOutline(true);

      // 清除任何现有的课程状态，确保显示的是新生成的内容
      setOutline(null);
      setContent(null);
      setSelectedSection(null);
      setCourseSource('');

      console.log('Creating new course for:', currentPrompt);
      toast.info('正在调用后端AI代理生成课程大纲...');

      // 检查 prompt 是否为空
      if (!currentPrompt) {
        console.error('=== CRITICAL: No prompt available during course creation ===');
        toast.error('学习主题丢失，请返回首页重新输入');
        setIsLoadingOutline(false);
        setIsCreatingCourse(false);
        return;
      }

      // 获取用户背景信息以传递给后端
      const userBg = userBackground || {};
      const learningGoal = userBg.learningGoal || '深入理解主题内容';
      const duration = userBg.timePreference || '4-6小时';
      const backgroundLevel = userBg.knowledgeLevel || '初学者';

      console.log('Calling backend course_planner.py with user background:', userBg);
      toast.info('正在使用course_planner.py代理生成个性化课程...');

      const data = await getCourseOutline(
        currentPrompt,
        learningGoal,
        duration,
        backgroundLevel
      );

      console.log('Raw API response from course_planner.py:', data);

      // 验证后端返回的数据
      if (!data || typeof data !== 'object') {
        throw new Error('Backend course_planner.py returned invalid data format');
      }

      // 验证必要字段
      if (!data.chapters || !Array.isArray(data.chapters) || data.chapters.length === 0) {
        throw new Error('Backend course_planner.py did not generate valid course chapters');
      }

      toast.success('✅ course_planner.py代理成功生成课程大纲！');

      // 验证和清理数据 - 支持新旧格式
      const validatedData: CourseOutline = {
        // 处理标题 - 支持新格式(course_title)和旧格式(title)
        title: data?.course_title || data?.title || `${currentPrompt} 课程大纲`,
        course_title: data?.course_title || data?.title || `${currentPrompt} 课程大纲`,
        course_description: data?.course_description,
        background_analysis: data?.background_analysis,
        bloom_taxonomy_objectives: data?.bloom_taxonomy_objectives,
        curriculum_alignment: data?.curriculum_alignment,
        chapters: data.chapters
      };

      // 确保每个章节都有必要的字段
      validatedData.chapters = validatedData.chapters.map((chapter, index) => ({
        ...chapter,
        id: chapter.id || `chapter_${index + 1}`,
        sections: Array.isArray(chapter.sections) ? chapter.sections.map((section, sectionIndex) => ({
          id: section.id || `${chapter.id || `chapter_${index + 1}`}_${sectionIndex + 1}`,
          title: section.title || `第${sectionIndex + 1}节`,
          content_type: section.content_type || '学习内容',
          activity_suggestion: section.activity_suggestion
        })) : [{
          id: `${chapter.id || `chapter_${index + 1}`}_1`,
          title: `${chapter?.title || `第${index + 1}章`} - 详细内容`
        }]
      }));

      // 立即显示大纲到左面板
      setOutline(validatedData);
      setCourseSource('api');
      setHasCourseGenerated(true);

      // 保存到localStorage
      saveCourseToStorage(currentPrompt, validatedData);

      // 选择第一个章节并开始生成内容到右面板
      if (validatedData.chapters.length > 0 && validatedData.chapters[0].sections.length > 0) {
        setSelectedSection(validatedData.chapters[0].sections[0].id);
        toast.success('✅ 课程大纲已显示在左侧，正在调用content_designer.py生成第一章内容...');
      } else {
        toast.success('✅ 课程大纲生成完成！');
      }

      console.log('=== handleCreateNewCourse completed successfully ===');
      console.log('Current location after completion:', window.location.pathname);

    } catch (error) {
      console.error('❌ Backend course generation failed:', error);

      // 提供详细的错误信息
      let errorMessage = '后端课程生成失败';
      if (error instanceof Error) {
        if (error.message.includes('course_planner.py')) {
          errorMessage = '❌ course_planner.py代理调用失败，请检查后端服务';
        } else if (error.message.includes('invalid data format')) {
          errorMessage = '❌ course_planner.py返回数据格式错误';
        } else if (error.message.includes('valid course chapters')) {
          errorMessage = '❌ course_planner.py未能生成有效的课程章节';
        } else {
          errorMessage = `❌ 后端错误: ${error.message}`;
        }
      }

      toast.error(errorMessage);

      // 不显示任何默认内容，保持加载状态直到用户重试
      // 这确保用户知道需要重新尝试生成，而不是看到虚假的默认内容
      setOutline(null);
      setContent(null);
      setSelectedSection(null);
      setCourseSource('');

      // 显示重试提示
      toast.info('💡 请重试课程生成，或检查网络连接和后端服务状态');

    } finally {
      setIsLoadingOutline(false);
      setIsCreatingCourse(false); // 重置创建状态
    }
  };

  // 生成缺失内容的函数
  const generateMissingContent = async (missingSectionIds: string[]) => {
    try {
      setIsGeneratingContent(true);

      // 初始化进度状态
      const initialProgress: {[key: string]: 'loading' | 'success' | 'error'} = {};
      missingSectionIds.forEach(sectionId => {
        initialProgress[sectionId] = 'loading';
      });
      setContentGenerationProgress(initialProgress);

      // 使用courseCompletenessChecker中的批量生成功能
      await generateAllMissingContent(
        initialPrompt!,
        missingSectionIds,
        (sectionId: string, status: 'loading' | 'success' | 'error') => {
          setContentGenerationProgress(prev => ({
            ...prev,
            [sectionId]: status
          }));
        }
      );

      // 检查是否所有内容都生成成功
      const allSuccess = missingSectionIds.every(sectionId =>
        contentGenerationProgress[sectionId] === 'success'
      );

      if (allSuccess) {
        toast.success('所有缺失内容已生成完成！');
      } else {
        toast.warning('部分内容生成失败，您可以稍后重试');
      }

    } catch (error) {
      console.error('Error generating missing content:', error);
      toast.error('生成缺失内容时出现错误');
    } finally {
      setIsGeneratingContent(false);
    }
  };

  // useEffect for loading section content
  useEffect(() => {
    const fetchSectionContent = async () => {
      if (!selectedSection || !initialPrompt) return;

      try {
        setIsLoadingContent(true);

        // 1. 首先检查localStorage缓存
        const cachedContent = loadSectionFromStorage(initialPrompt, selectedSection);
        if (cachedContent) {
          console.log('✅ Using cached section content from localStorage');
          setContent(cachedContent);
          setIsLoadingContent(false);
          toast.success('✅ 已加载缓存的章节内容');
          return;
        }

        // 2. 如果没有缓存，调用后端content_designer.py代理生成内容
        console.log('🔄 Calling backend content_designer.py for section:', selectedSection);
        toast.info('🤖 正在调用content_designer.py代理生成章节内容...');

        const data = await getCourseContent(selectedSection, initialPrompt);

        // 验证content_designer.py返回的数据
        if (!data || typeof data !== 'object') {
          throw new Error('content_designer.py returned invalid data format');
        }

        if (!data.title && !data.mainContent) {
          throw new Error('content_designer.py did not generate valid content structure');
        }

        console.log('✅ content_designer.py successfully generated content');
        setContent(data);

        // 保存到localStorage
        saveSectionToStorage(initialPrompt, selectedSection, data);
        toast.success('✅ content_designer.py代理成功生成章节内容！');

      } catch (error) {
        console.error('❌ Backend content generation failed:', error);

        // 提供详细的错误信息
        let errorMessage = '章节内容生成失败';
        if (error instanceof Error) {
          if (error.message.includes('content_designer.py')) {
            errorMessage = '❌ content_designer.py代理调用失败，请检查后端服务';
          } else if (error.message.includes('invalid data format')) {
            errorMessage = '❌ content_designer.py返回数据格式错误';
          } else if (error.message.includes('valid content structure')) {
            errorMessage = '❌ content_designer.py未能生成有效的内容结构';
          } else {
            errorMessage = `❌ 后端错误: ${error.message}`;
          }
        }

        toast.error(errorMessage);

        // 不设置任何默认内容，保持空状态
        setContent(null);

        // 显示重试提示
        toast.info('💡 请重新选择章节重试，或检查后端服务状态');

      } finally {
        setIsLoadingContent(false);
      }
    };

    fetchSectionContent();
  }, [selectedSection, initialPrompt]);

  const handleStartLearning = () => {
    navigate('/interactive-learning');
  };

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      <Navbar />

      {/* Course Selection Dialog */}
      <CourseSelectionDialog
        isOpen={showCourseDialog}
        onClose={() => setShowCourseDialog(false)}
        topic={initialPrompt || ''}
        backgroundInfo={`学习主题: ${initialPrompt}`}
        onSelectExisting={handleSelectExistingCourse}
        onCreateNew={handleCreateNewCourse}
      />

      <main className="flex-grow flex">
        {/* Left sidebar - Course overview and navigation (fixed width, scrollable) */}
        <div className="w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col h-[calc(100vh-4rem)]">

          {/* Course Overview Section - Fixed at top */}
          <div className="p-6 border-b border-gray-100 bg-gradient-to-r from-blue-50 to-indigo-50">
            <div className="flex items-start justify-between mb-3">
              <h2 className="text-lg font-bold text-gray-900 font-display">课程概览</h2>
              {courseSource && (
                <div className={`text-xs px-2 py-1 rounded-full font-medium ${
                  courseSource === 'localStorage' ? 'bg-green-100 text-green-700' :
                  courseSource === 'memory' ? 'bg-purple-100 text-purple-700' :
                  courseSource === 'file' ? 'bg-blue-100 text-blue-700' :
                  courseSource === 'api' ? 'bg-orange-100 text-orange-700' :
                  'bg-gray-100 text-gray-700'
                }`}>
                  {courseSource === 'localStorage' && '📱 本地缓存'}
                  {courseSource === 'memory' && '🧠 服务器记忆'}
                  {courseSource === 'file' && '📄 文件存储'}
                  {courseSource === 'api' && '✨ 新生成'}
                  {courseSource === 'fallback' && '⚠️ 默认模板'}
                </div>
              )}
            </div>

            {isLoadingOutline ? (
              <LoadingPlaceholder lines={4} />
            ) : outline ? (
              <div>
                <h3 className="text-base font-semibold text-gray-800 mb-2 leading-tight">
                  {outline.title || outline.course_title}
                </h3>

                {outline.course_description && (
                  <p className="text-sm text-gray-600 mb-3 line-clamp-3">{outline.course_description}</p>
                )}

                {outline.background_analysis && (
                  <div className="flex flex-wrap gap-2 mb-3">
                    {outline.background_analysis.target_age && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-green-100 text-green-700">
                        👥 {outline.background_analysis.target_age}
                      </span>
                    )}
                    {outline.background_analysis.knowledge_level && (
                      <span className="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-700">
                        📚 {outline.background_analysis.knowledge_level}
                      </span>
                    )}
                  </div>
                )}

                {/* 课程统计 */}
                {outline.chapters && outline.chapters.length > 0 && (
                  <div className="text-xs text-gray-600 bg-gray-50 p-2 rounded-lg">
                    {(() => {
                      const totalSections = outline.chapters.reduce((acc, chapter) => acc + (chapter.sections?.length || 0), 0);
                      const cachedSections = outline.chapters.reduce((acc, chapter) =>
                        acc + (chapter.sections?.filter(section => checkSectionCached(section.id)).length || 0), 0);
                      return (
                        <div className="flex justify-between items-center">
                          <span>📊 课程进度</span>
                          <span>{cachedSections}/{totalSections} 章节已缓存</span>
                        </div>
                      );
                    })()}
                  </div>
                )}
              </div>
            ) : (
              <p className="text-sm text-gray-500">加载中...</p>
            )}
          </div>

          {/* Learning Objectives Section - Collapsible */}
          {outline?.bloom_taxonomy_objectives && (
            <div className="p-4 bg-purple-25">
              <details className="group">
                <summary className="cursor-pointer text-sm font-medium text-purple-800 mb-2 list-none flex items-center justify-between">
                  <span>🎯 学习目标 (布鲁姆分类)</span>
                  <svg className="w-4 h-4 transition-transform group-open:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </summary>
                <div className="mt-2 space-y-1 text-xs text-purple-700">
                  {Object.entries(outline.bloom_taxonomy_objectives).map(([level, objectives]) => {
                    const objectiveArray = objectives as string[];
                    return objectiveArray && objectiveArray.length > 0 && (
                      <div key={level} className="flex">
                        <span className="font-medium capitalize w-16 flex-shrink-0">{level}:</span>
                        <span className="flex-1">{objectiveArray.slice(0, 1).join('、')}
                          {objectiveArray.length > 1 && `等${objectiveArray.length}项`}
                        </span>
                      </div>
                    );
                  })}
                </div>
              </details>
            </div>
          )}

          {/* 固定的课程章节标题 - 毛玻璃效果，最高 Z-index，紧贴学习目标区域 */}
          <div className="sticky top-0 z-50 backdrop-blur-md bg-white/90 border-t border-gray-200/60 shadow-sm">
            <h4 className="text-sm font-medium text-gray-900 px-4 py-3 flex items-center space-x-2">
              <span>📖 课程章节</span>
              {outline?.chapters && (
                <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs bg-blue-100 text-blue-700 ml-auto">
                  {outline.chapters.length}章
                </span>
              )}
            </h4>
          </div>

          {/* Course Navigation - Scrollable */}
          <div className="flex-1 overflow-y-auto px-4 pb-4">
            {isLoadingOutline ? (
              <div className="space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <div className="flex items-center space-x-3">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
                    <div>
                      <p className="text-sm font-medium text-blue-800">🤖 正在调用course_planner.py代理</p>
                      <p className="text-xs text-blue-600">AI正在根据您的背景信息生成个性化课程大纲...</p>
                    </div>
                  </div>
                </div>
                <LoadingPlaceholder lines={8} />
              </div>
            ) : outline && outline.chapters ? (
              <div className="space-y-3">
                {outline.chapters.map((chapter, chapterIndex) => (
                  <div key={chapter.id} className="space-y-2">
                    {/* Chapter header */}
                    <div className="bg-gray-50 p-3 rounded-lg border border-gray-100 hover:bg-gray-100 transition-colors">
                      <h5 className="font-medium text-gray-900 text-sm mb-1">{chapter.title}</h5>
                      {chapter.description && (
                        <p className="text-xs text-gray-600 mb-2 line-clamp-2">{chapter.description}</p>
                      )}

                      <div className="flex flex-wrap gap-1 mb-2">
                        {chapter.estimated_duration && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-blue-100 text-blue-700">
                            ⏱️ {chapter.estimated_duration}
                          </span>
                        )}
                        {chapter.bloom_focus && chapter.bloom_focus.length > 0 && (
                          <span className="inline-flex items-center px-2 py-0.5 rounded text-xs bg-purple-100 text-purple-700">
                            🎯 {chapter.bloom_focus.slice(0, 2).join('、')}
                          </span>
                        )}
                      </div>

                      {chapter.key_concepts && chapter.key_concepts.length > 0 && (
                        <div className="text-xs text-gray-600">
                          <span className="font-medium">核心概念:</span> {chapter.key_concepts.slice(0, 2).join('、')}
                          {chapter.key_concepts.length > 2 && '...'}
                        </div>
                      )}
                    </div>

                    {/* Chapter sections */}
                    <div className="ml-4 space-y-1">
                      {chapter.sections && chapter.sections.length > 0 ? (
                        chapter.sections.map((section, sectionIndex) => (
                          <button
                            key={section.id}
                            onClick={() => setSelectedSection(section.id)}
                            className={`text-left w-full p-2 text-sm rounded-lg border transition-all duration-200 relative ${
                              selectedSection === section.id
                                ? 'bg-primary text-white border-primary shadow-md z-10'
                                : 'text-gray-700 border-gray-200 hover:bg-gray-50 hover:border-gray-300 z-0'
                            }`}
                          >
                            <div className="flex items-center justify-between">
                              <span className="font-medium">{chapterIndex + 1}.{sectionIndex + 1} {section.title}</span>
                              <div className="flex items-center space-x-1">
                                {checkSectionCached(section.id) && (
                                  <span className="text-xs text-green-600" title="已缓存内容">💾</span>
                                )}
                                {selectedSection === section.id && (
                                  <svg className="w-4 h-4 ml-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path fillRule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clipRule="evenodd" />
                                  </svg>
                                )}
                              </div>
                            </div>
                            {section.content_type && (
                              <div className="text-xs text-gray-500 mt-1">
                                📄 {section.content_type}
                              </div>
                            )}
                          </button>
                        ))
                      ) : (
                        <div className="text-sm text-gray-500 p-2 italic">暂无章节内容</div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center text-gray-500 text-sm">暂无课程章节</div>
            )}
          </div>

          {/* Action buttons - Fixed at bottom */}
          <div className="p-4 border-t border-gray-100 bg-white">
            <Button onClick={handleStartLearning} className="w-full" size="sm">
              🚀 开始互动学习
            </Button>
          </div>
        </div>

        {/* Right area - Detailed content */}
        <div className="flex-1 flex flex-col h-[calc(100vh-4rem)]">

          {/* Content header - Fixed */}
          <div className="bg-white border-b border-gray-200 px-8 py-4 shadow-sm">
            {selectedSection && content ? (
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 font-display">{content.title}</h2>
                  <p className="text-sm text-gray-600 mt-1">当前学习章节</p>
                </div>
                <Button onClick={handleStartLearning} size="sm" variant="outline">
                  开始练习
                </Button>
              </div>
            ) : selectedSection && isLoadingContent ? (
              <div className="flex items-center justify-between">
                <div>
                  <h2 className="text-xl font-bold text-gray-900 font-display">正在加载章节内容...</h2>
                  <p className="text-sm text-gray-600 mt-1">请稍候，正在为您准备学习资料</p>
                </div>
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div>
                <h2 className="text-xl font-bold text-gray-900 font-display">课程内容</h2>
                <p className="text-sm text-gray-600 mt-1">请从左侧选择要学习的章节</p>
              </div>
            )}
          </div>

          {/* Content body - Scrollable */}
          <div className="flex-1 overflow-y-auto p-8 bg-gray-50">
            {isLoadingContent ? (
              <div className="max-w-4xl mx-auto space-y-8">
                <div className="bg-green-50 border border-green-200 rounded-xl p-6">
                  <div className="flex items-center space-x-3 mb-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-600"></div>
                    <div>
                      <p className="text-sm font-medium text-green-800">🤖 正在调用content_designer.py代理</p>
                      <p className="text-xs text-green-600">AI正在为当前章节生成详细的学习内容...</p>
                    </div>
                  </div>
                  <div className="space-y-2 text-xs text-green-700">
                    <p>• 分析章节主题和学习目标</p>
                    <p>• 生成结构化的学习内容</p>
                    <p>• 提取核心知识点和关键概念</p>
                    <p>• 匹配课程标准和教学要求</p>
                  </div>
                </div>
                <LoadingPlaceholder type="image" />
                <LoadingPlaceholder lines={8} />
                <LoadingPlaceholder lines={3} />
              </div>
            ) : content ? (
              <div className="max-w-4xl mx-auto space-y-8">

                {/* Images section */}
                {content.images && content.images.length > 0 && (
                  <div className="bg-white rounded-xl shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                      🖼️ 图片资源
                    </h3>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      {content.images.map((image, index) => (
                        <figure key={index} className="rounded-lg overflow-hidden shadow-md hover:shadow-lg transition-shadow">
                          <img
                            src={image.url}
                            alt={image.caption}
                            className="w-full h-48 object-cover"
                          />
                          <figcaption className="p-3 text-sm text-gray-700 bg-gray-50">
                            {image.caption}
                          </figcaption>
                        </figure>
                      ))}
                    </div>
                  </div>
                )}

                {/* Main content */}
                <div className="bg-white rounded-xl shadow-sm p-6">
                  <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                    📚 主要内容
                  </h3>
                  <div className="prose prose-gray max-w-none">
                    <MarkdownRenderer content={content.mainContent} />
                  </div>
                </div>

                {/* Key points */}
                {content.keyPoints && content.keyPoints.length > 0 && (
                  <div className="bg-white rounded-xl shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                      🎯 核心知识点
                    </h3>
                    <div className="grid gap-3">
                      {content.keyPoints.map((point, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-blue-50 rounded-lg border border-blue-100">
                          <div className="flex-shrink-0 w-6 h-6 bg-blue-500 text-white rounded-full flex items-center justify-center text-sm font-medium">
                            {index + 1}
                          </div>
                          <p className="text-gray-800 flex-1">{point}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Curriculum alignment */}
                {content.curriculumAlignment && content.curriculumAlignment.length > 0 && (
                  <div className="bg-white rounded-xl shadow-sm p-6">
                    <h3 className="text-lg font-semibold mb-4 text-gray-900 flex items-center">
                      📋 课标对齐
                    </h3>
                    <div className="space-y-2">
                      {content.curriculumAlignment.map((alignment, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 rounded-lg border border-green-100">
                          <div className="flex-shrink-0 w-5 h-5 text-green-600 mt-0.5">
                            <svg fill="currentColor" viewBox="0 0 20 20">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                          </div>
                          <p className="text-gray-800 flex-1">{alignment}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Call to action */}
                <div className="text-center bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-8 text-white">
                  <h3 className="text-xl font-bold mb-2">准备好开始学习了吗？</h3>
                  <p className="text-blue-100 mb-4">开启您的互动学习之旅</p>
                  <Button onClick={handleStartLearning} size="lg" variant="secondary" className="bg-white text-blue-600 hover:bg-gray-100">
                    🚀 开始互动学习
                  </Button>
                </div>

              </div>
            ) : selectedSection ? (
              <div className="flex flex-col items-center justify-center h-full text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                  <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <h3 className="text-lg font-medium text-gray-900 mb-2">正在加载内容...</h3>
                <p className="text-gray-500">请稍候，我们正在为您准备学习资料</p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-center">
                {outline && outline.chapters && outline.chapters.length > 0 ? (
                  // 有课程大纲，提示选择章节
                  <>
                    <div className="w-20 h-20 bg-gradient-to-br from-blue-100 to-indigo-200 rounded-full flex items-center justify-center mb-6">
                      <svg className="w-10 h-10 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <h3 className="text-xl font-bold text-gray-900 mb-3">开始您的学习之旅</h3>
                    <p className="text-gray-600 max-w-md mb-6">
                      您的课程大纲已准备完毕！请从左侧选择一个章节开始学习，我们将为您提供详细的学习内容和互动资源。
                    </p>
                    {outline.chapters.length > 0 && (
                      <div className="bg-blue-50 rounded-lg p-4 border border-blue-200">
                        <p className="text-sm text-blue-800 font-medium mb-2">💡 提示</p>
                        <p className="text-sm text-blue-700">
                          建议从第一章开始学习：<strong>{outline.chapters[0]?.title}</strong>
                        </p>
                      </div>
                    )}
                  </>
                ) : (
                  // 没有课程大纲，通用提示
                  <>
                    <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 15l-2 5L9 9l11 4-5 2zm0 0l5 5M7.188 2.239l.777 2.897M5.136 7.965l-2.898-.777M13.95 4.05l-2.122 2.122m-5.657 5.656l-2.12 2.122" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">课程准备中</h3>
                    <p className="text-gray-500 max-w-md">
                      课程大纲加载完成后，您可以从左侧选择章节开始学习
                    </p>
                  </>
                )}
              </div>
            )}
          </div>
        </div>
      </main>
    </div>
  );
};

export default CoursePlanning;
