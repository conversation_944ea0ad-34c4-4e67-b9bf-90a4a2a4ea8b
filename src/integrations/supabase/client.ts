// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://xedsxadzzdayobbvwntq.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhlZHN4YWR6emRheW9iYnZ3bnRxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc3NDQ2NDQsImV4cCI6MjA2MzMyMDY0NH0.FIPSg8DsZ-7OVt_fDzGcGQJEnWqN7q8Y-RroYM-tdMw";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);