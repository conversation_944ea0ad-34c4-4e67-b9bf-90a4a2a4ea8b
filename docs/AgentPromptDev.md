# 记录对Agent功能的开发过程

## CoursePlanner

--- 
仔细阅读下面对于当前 CoursePlanner Agent 的分析，做出相应更改：

# 1. 改进提示词，以更好的统领后续任务
## 问题：当前的 CoursePlanner Agent 只关注课程大纲的生成，而并不能有效的指导 ContentDesigner Agent 生成符合大纲要求的有效的课程内容，二者非常不一致！因此需要重新设计 json 文件的架构，使其在 @CoursePlanning.tsx 页面上能够显示更丰富的信息。
## 任务：一步一步深度思考 json 模板的架构和内容编排：
### 1.1. 首先明确这个 json 文件包括如下两个关键功能：
- 生成详尽、合理的课程大纲，供前端页面 @CoursePlanning.tsx 显示
- 更重要的，它要能够指导 `ContentDesigner` Agent 进行逐个章节的内容生成，使其始终围绕大纲中确定的课程内容、学习目标等条目来设计课程内容，不能偏离大纲
### 1.2. 重新规划 大纲 所要包含的内容，保证能将对 ContentDesigner 的生成要求，准确传递过去！因此不仅需要包括针对学习主题的内容规划，还需要包括对于课程内容的设计要求，具体参考下面的要求：
- 课程名称
- 主题课程概述
- 课程目标：按照布鲁姆的认知分层理论来确定每个层级的目标，不要求全部覆盖，但是要分配合理
- 章节划分
  - 各章节名称
  - 每个章节预计时间分配
  - 章节内容包含哪些种类的教学资源（活动）：如代码、实验、问答、游戏等
- 对每一节内容，要生成用于指导`ContentDesigner` 进行内容设计的具体方案，包括但不限于：
  - 每个章节的内容是如何支撑本课程目标的
  - 每个章节使用哪些类型的教学资源（活动）更合适

# 2. 改进提示词，以约束 json 文件的生成格式
## 问题：目前由 @course_planner 这个Agent生成的 json 文件并没有完全遵循其中的 json 模板规定的格式！
## 任务： 在确定了由上面对课程大纲的具体要求之后，根据大纲来修正 `course_planner`的提示词系统，让生成的 json 内容符合大纲的结构，格式必须严格遵循 json 模板的规定；

---
