# 记录对Agent功能的开发过程

## CoursePlanner

--- 
仔细阅读下面对于当前 CoursePlanner Agent 的分析，做出相应更改：

# 1. 改进提示词，以更好的统领后续任务
## 问题：当前的 CoursePlanner Agent 只关注课程大纲的生成，而并不能有效的指导 ContentDesigner Agent 生成符合大纲要求的有效的课程内容，二者非常不一致！因此需要重新设计 json 文件的架构，使其在 @CoursePlanning.tsx 页面上能够显示更丰富的信息。
## 任务：一步一步深度思考 json 模板的架构和内容编排：
### 1.1. 首先明确这个 json 文件包括如下两个关键功能：
- 生成详尽、合理的课程大纲，供前端页面 @CoursePlanning.tsx 显示
- 更重要的，它要能够指导 `ContentDesigner` Agent 进行逐个章节的内容生成，使其始终围绕大纲中确定的课程内容、学习目标等条目来设计课程内容，不能偏离大纲
### 1.2. 重新规划 大纲 所要包含的内容，保证能将对 ContentDesigner 的生成要求，准确传递过去！因此不仅需要包括针对学习主题的内容规划，还需要包括对于课程内容的设计要求，具体参考下面的要求：
- 课程名称
- 主题课程概述
- 课程目标：按照布鲁姆的认知分层理论来确定每个层级的目标，不要求全部覆盖，但是要分配合理
- 章节划分
  - 各章节名称
  - 每个章节预计时间分配
  - 章节内容包含哪些种类的教学资源（活动）：如代码、实验、问答、游戏等
- 对每一节内容，要生成用于指导`ContentDesigner` 进行内容设计的具体方案，包括但不限于：
  - 每个章节的内容是如何支撑本课程目标的
  - 每个章节使用哪些类型的教学资源（活动）更合适

# 2. 改进提示词，以约束 json 文件的生成格式
## 问题：目前由 @course_planner 这个Agent生成的 json 文件并没有完全遵循其中的 json 模板规定的格式！
## 任务： 在确定了由上面对课程大纲的具体要求之后，根据大纲来修正 `course_planner`的提示词系统，让生成的 json 内容符合大纲的结构，格式必须严格遵循 json 模板的规定；

---

目前本项目运行并不稳定，运行时有逻辑问题，目前已发现的如下：

# 1. 页面内容加载逻辑问题

- 目前问题：在记录了用户输入的topic和用户背景信息之后，系统加载@src/pages/CoursePlanning.tsx的课程列表弹出页面，当用户选择新建或已存在课程之后，页面内容加载逻辑发生混乱，内容不能正确加载至主页面的对应位置；

- 正确逻辑：系统首先根据关键词，在弹出页面列出所有已存在的相关课程的列表（需要显示出课程名，此课程对应的用户背景信息），接下来分两种情况：
    - 如果该课程不存在，则需要新建课程，将用户主题和背景信息传递给@backend/src/agents/teaching_team/course_planner.py agent 进行大纲生成，生成结果返回后，马上显示在@src/pages/CoursePlanning.tsx页面的左侧对应栏目中，然后从第一章开始，逐章节生成内容！这部分生成任务是调用@backend/src/agents/teaching_team/content_designer.py agent来做的，生成完毕后，将结果显示在页面右侧位置，接下来，用户点击哪个章节，哪个章节内容优先触发生成操作；
    - 如果课程存在，则需要检查课程内容完整性，两种情形，（a）如果内容完整，即课程大纲和至少一章的课程内容都存在，则将其信息加载到页面上；（b）如果内容不完整，只有课程大纲（课程大纲的所有必要组件都完整），则将大纲内容加载到页面左侧，同时页面右侧显示图形占位元素，告知用户正在生成内容；（c）如果课程大纲都有缺失，则如同生成新课程一样处理。

- 任务：按照上面描述的正确逻辑，检查现有代码并做出修改！

- 正确逻辑：@backend/src/agents/teaching_team/teacher_agent.py 应该能够针对用户提出的问题，结合主题、章节内容、用户背景信息，三者，做出合理的回复，并与用户持续交流；

- 任务：按照正确逻辑描述，检查代码问题并修正。




---

The project currently has stability issues with runtime logic problems. Two main issues have been identified that need to be fixed:

## Issue 1: Course Content Loading Logic Problems

**Current Problem:** After recording user input (topic and background information), the system loads the course list popup page at `@src/pages/CoursePlanning.tsx`. When users select either "create new course" or an existing course, the page content loading logic becomes confused and content cannot be properly loaded to the corresponding positions on the main page.

**Correct Logic:** The system should follow this workflow:
1. First, based on keywords, display a list of all existing related courses in the popup page (must show course name and corresponding user background information for each course)
2. Then handle two scenarios:

   **Scenario A - Course doesn't exist (new course creation):**
   - Pass user topic and background information to `@backend/src/agents/teaching_team/course_planner.py` agent to generate course outline
   - Once outline generation is complete, immediately display results in the left panel of `@src/pages/CoursePlanning.tsx` page
   - Start generating content chapter by chapter from Chapter 1 using `@backend/src/agents/teaching_team/content_designer.py` agent
   - Display generated content in the right panel of the page
   - When users click on any chapter, prioritize content generation for that specific chapter

   **Scenario B - Course exists (load existing course):**
   - Check course content completeness with three sub-cases:
     - **(a) Complete content:** If both course outline and at least one chapter's content exist, load all information to the page
     - **(b) Incomplete content:** If only course outline exists (with all necessary outline components complete), load outline to left panel and display placeholder graphics on right panel informing user that content is being generated
     - **(c) Missing outline:** If course outline is incomplete or missing, treat as new course creation (follow Scenario A)

**Task:** Review existing code according to the correct logic described above and implement necessary modifications.

## Issue 2: Interactive Chat Functionality Missing

**Current Problem:** On the `@src/pages/InteractiveLearning.tsx` page, chat messages sent by users cannot be properly responded to by the backend `@backend/src/agents/teaching_team/teacher_agent.py`, causing the chat functionality to fail.

**Correct Logic:** The `@backend/src/agents/teaching_team/teacher_agent.py` should be able to:
- Respond to user questions by combining three elements: topic, chapter content, and user background information
- Provide reasonable replies based on this combined context
- Maintain continuous conversation flow with users

**Task:** Review the code according to the correct logic description and fix the identified issues.

**Implementation Requirements:**
- Analyze the current codebase to understand the existing implementation
- Identify specific code sections causing the logic problems
- Implement fixes that align with the described correct workflows
- Ensure proper data flow between frontend components and backend agents
- Test the fixes to verify correct functionality