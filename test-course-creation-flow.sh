#!/bin/bash

echo "=== 开始测试课程创建流程 ==="

# 1. 测试API连接
echo "1. 测试后端API连接..."
curl -s http://localhost:8000/api/course/list > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ 后端API连接正常"
else
    echo "❌ 后端API连接失败"
    exit 1
fi

# 2. 测试课程生成API
echo "2. 测试课程生成API..."
curl -s -X POST http://localhost:8000/api/course/generate \
  -H "Content-Type: application/json" \
  -d '{
    "topic": "测试主题",
    "learning_goal": "学习目标",
    "time_preference": "1-2小时",
    "background_knowledge": "初学者"
  }' > /dev/null

if [ $? -eq 0 ]; then
    echo "✅ 课程生成API连接正常"
else
    echo "❌ 课程生成API可能有问题"
fi

echo "3. 请在浏览器中手动测试以下流程："
echo "   - 访问 http://localhost:8080"
echo "   - 输入学习主题：'测试课程创建'"
echo "   - 完成背景信息问卷"
echo "   - 在课程选择对话框中点击'生成新课程'"
echo "   - 观察控制台日志，查看是否有重定向问题"
echo ""
echo "4. 关键调试点："
echo "   - 查看控制台是否出现 '=== handleCreateNewCourse started ==='"
echo "   - 查看是否出现 '=== No initialPrompt, navigating to home ==='"
echo "   - 检查页面URL是否保持在 /course-planning"
echo ""
echo "=== 测试脚本完成 ==="
