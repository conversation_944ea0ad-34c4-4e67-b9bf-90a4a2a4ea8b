# 思考内容展开功能说明

## 功能概述

为了提升用户体验，现在聊天界面会自动分离AI模型的思考过程和实际回复内容：

- **默认显示**：只显示AI的实际回复内容
- **可选查看**：通过"思考过程"按钮展开查看AI的内部思考逻辑
- **用户消息**：用户消息保持原样显示，不受影响

## 技术实现

### 1. 内容解析
```typescript
// 解析消息内容，分离思考内容和实际回复
const parseMessageContent = (content: string) => {
  // 匹配 <think>...</think> 或 <thinking>...</thinking> 标签
  const thinkRegex = /<think(?:ing)?>([\s\S]*?)<\/think(?:ing)?>/gi;
  
  let thinkingContent = '';
  let actualContent = content;
  
  // 提取思考内容
  const thinkMatches = content.match(thinkRegex);
  if (thinkMatches) {
    thinkingContent = thinkMatches.map(match => {
      // 移除标签，保留内容
      return match.replace(/<\/?think(?:ing)?>/gi, '').trim();
    }).join('\n\n');
    
    // 从实际内容中移除思考部分
    actualContent = content.replace(thinkRegex, '').trim();
  }
  
  return {
    thinkingContent,
    actualContent: actualContent || content
  };
};
```

### 2. UI 组件
- **思考过程按钮**：使用脑图标 + "思考过程" 文字
- **展开/收起图标**：右箭头/下箭头指示状态
- **思考内容样式**：灰色背景，蓝色左边框，等宽字体显示
- **响应式设计**：适配不同屏幕尺寸

## 支持的标签格式

该功能支持以下思考标签格式：
- `<think>...</think>`
- `<thinking>...</thinking>`

## 用户界面

### 默认状态
```
┌─────────────────────────────────────┐
│ 🧠 思考过程 ▶                       │
│                                     │
│ 这里是AI的实际回复内容...            │
│                           10:30 AM  │
└─────────────────────────────────────┘
```

### 展开状态
```
┌─────────────────────────────────────┐
│ 🧠 思考过程 ▼                       │
│ ┌─────────────────────────────────┐ │
│ │ 🧠 AI思考过程                   │ │
│ │ 这里显示AI的内部思考逻辑...      │ │
│ │ 使用等宽字体保持格式            │ │
│ └─────────────────────────────────┘ │
│                                     │
│ 这里是AI的实际回复内容...            │
│                           10:30 AM  │
└─────────────────────────────────────┘
```

## 特性优势

1. **清晰分离**：思考过程和实际回复内容清晰分离
2. **用户选择**：用户可以自主选择是否查看思考过程
3. **默认简洁**：默认只显示实际回复，保持界面简洁
4. **教育价值**：展开思考过程有助于理解AI的推理逻辑
5. **调试友好**：开发者可以通过思考过程了解模型行为

## 适用场景

- **学习辅导**：学生可以查看AI的解题思路
- **问题分析**：了解AI如何分析复杂问题
- **逻辑推理**：查看AI的推理步骤和逻辑链
- **调试开发**：开发者检查模型输出质量

## 注意事项

1. **标签规范**：确保AI模型输出使用标准的思考标签格式
2. **内容清理**：自动移除多余的空白和格式字符
3. **性能考虑**：对于长文本的解析效率优化
4. **多语言支持**：思考内容可能包含多种语言

## 测试用例

### 测试消息示例
```
<thinking>
用户问的是关于Python编程的问题。我需要：
1. 先解释基本概念
2. 提供具体示例
3. 给出实践建议
</thinking>

Python是一种高级编程语言，具有简洁清晰的语法。

以下是一个简单的Python示例：
```python
print("Hello, World!")
```

建议从基础语法开始学习，逐步深入。
```

### 期望显示结果
- **默认**：只显示实际回复内容（Python解释和示例）
- **展开**：显示完整的思考过程 + 实际回复内容

## 后续改进

1. **智能识别**：自动识别更多思考标签格式
2. **样式优化**：提供更多主题和样式选项
3. **导出功能**：支持导出包含思考过程的完整对话
4. **统计分析**：分析用户对思考过程的查看习惯
5. **个性化设置**：允许用户设置默认展开/收起偏好

现在用户可以享受更清爽的对话体验，同时保留查看AI思考过程的能力！ 