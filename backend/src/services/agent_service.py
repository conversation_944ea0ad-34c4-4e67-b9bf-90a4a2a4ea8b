from typing import Dict, Any, List, Optional
import logging
from fastapi import WebSocket
import json
from datetime import datetime
import os
import hashlib

from src.agents.teaching_team.course_planner import course_planner
from src.agents.teaching_team.content_designer import content_designer
from src.agents.teaching_team.teacher_agent import teacher
from src.memory.course_memory import CourseMemory

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AgentService:
    """
    Agent服务类，负责协调不同Agent的工作，管理与前端的通信
    """

    def __init__(self):
        self.active_connections: Dict[str, WebSocket] = {}
        # 初始化课程记忆管理器
        self.course_memory = CourseMemory()
        # 确保本地课程存储目录存在
        self.courses_dir = os.path.join(os.path.dirname(__file__), "../../courses")
        os.makedirs(self.courses_dir, exist_ok=True)
        # 存储用户背景信息的字典
        self.user_backgrounds: Dict[str, Dict[str, Any]] = {}
        logger.info("AgentService initialized with course memory")

    def _generate_topic_id(self, topic: str) -> str:
        """为主题生成唯一ID"""
        return hashlib.md5(topic.lower().encode()).hexdigest()[:12]

    def _save_course_to_file(self, topic: str, course_data: Dict[str, Any]) -> str:
        """将课程数据保存到本地文件"""
        topic_id = self._generate_topic_id(topic)
        filename = f"course_{topic_id}.json"
        filepath = os.path.join(self.courses_dir, filename)

        # 添加保存时间戳
        course_data["saved_at"] = datetime.now().isoformat()
        course_data["topic"] = topic

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(course_data, f, ensure_ascii=False, indent=2)

        logger.info(f"Course saved to file: {filepath}")
        return filepath

    def _load_course_from_file(self, topic: str) -> Optional[Dict[str, Any]]:
        """从本地文件加载课程数据"""
        topic_id = self._generate_topic_id(topic)
        filename = f"course_{topic_id}.json"
        filepath = os.path.join(self.courses_dir, filename)

        if os.path.exists(filepath):
            try:
                with open(filepath, 'r', encoding='utf-8') as f:
                    course_data = json.load(f)
                logger.info(f"Course loaded from file: {filepath}")
                return course_data
            except Exception as e:
                logger.error(f"Error loading course from file {filepath}: {e}")
        return None

    def _save_section_to_file(self, topic: str, section_id: str, content_data: Dict[str, Any]) -> str:
        """将章节内容保存到本地文件"""
        topic_id = self._generate_topic_id(topic)
        section_filename = f"section_{topic_id}_{section_id.replace('.', '_')}.json"
        section_filepath = os.path.join(self.courses_dir, section_filename)

        # 添加保存时间戳
        content_data["saved_at"] = datetime.now().isoformat()
        content_data["topic"] = topic
        content_data["section_id"] = section_id

        with open(section_filepath, 'w', encoding='utf-8') as f:
            json.dump(content_data, f, ensure_ascii=False, indent=2)

        logger.info(f"Section content saved to file: {section_filepath}")
        return section_filepath

    def _load_section_from_file(self, topic: str, section_id: str) -> Optional[Dict[str, Any]]:
        """从本地文件加载章节内容"""
        topic_id = self._generate_topic_id(topic)
        section_filename = f"section_{topic_id}_{section_id.replace('.', '_')}.json"
        section_filepath = os.path.join(self.courses_dir, section_filename)

        if os.path.exists(section_filepath):
            try:
                with open(section_filepath, 'r', encoding='utf-8') as f:
                    content_data = json.load(f)
                logger.info(f"Section content loaded from file: {section_filepath}")
                return content_data
            except Exception as e:
                logger.error(f"Error loading section from file {section_filepath}: {e}")
        return None

    async def connect(self, websocket: WebSocket, client_id: str) -> None:
        """注册WebSocket连接"""
        await websocket.accept()
        self.active_connections[client_id] = websocket
        logger.info(f"Client {client_id} connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, client_id: str) -> None:
        """移除WebSocket连接"""
        if client_id in self.active_connections:
            del self.active_connections[client_id]
            logger.info(f"Client {client_id} disconnected. Total connections: {len(self.active_connections)}")

    async def send_message(self, client_id: str, message: Dict[str, Any]) -> None:
        """发送消息到特定客户端"""
        if client_id in self.active_connections:
            websocket = self.active_connections[client_id]
            await websocket.send_json(message)
            logger.debug(f"Message sent to client {client_id}")

    async def broadcast(self, message: Dict[str, Any], exclude: Optional[str] = None) -> None:
        """广播消息到所有客户端，可选择排除特定客户端"""
        for client_id, websocket in self.active_connections.items():
            if exclude is None or client_id != exclude:
                await websocket.send_json(message)
        logger.debug(f"Message broadcasted to {len(self.active_connections) - (1 if exclude else 0)} clients")

    async def process_message(self, client_id: str, message_content: str) -> Dict[str, Any]:
        """
        处理从客户端接收到的消息
        这里将解析用户背景信息并根据消息内容，决定调用哪个Agent来处理
        """
        # 解析消息中的用户背景信息
        user_background = self._extract_user_background(message_content)

        # 检查是否是课程规划请求（包含用户背景信息的首次请求）
        if user_background and self._is_course_planning_request(message_content):
            logger.info(f"检测到课程规划请求，用户背景信息: {user_background}")

            # 提取原始学习主题
            original_topic = self._extract_original_topic(message_content)

            if original_topic:
                # 调用课程规划Agent，传入用户背景信息
                course_plan = await self.create_course_plan_with_background(
                    topic=original_topic,
                    user_background=user_background
                )

                # 返回课程规划结果
                response = {
                    "content": f"已为您生成《{original_topic}》的个性化课程大纲！\n\n根据您的背景信息（{user_background.get('age', '')}，学习目标：{user_background.get('learningGoal', '')}，时间偏好：{user_background.get('timePreference', '')}），我为您量身定制了以下学习计划。请在课程规划页面查看详细内容。",
                    "sender": "agent",
                    "course_data": course_plan,
                    "user_background": user_background
                }
            else:
                response = {
                    "content": "抱歉，我无法从您的消息中识别出具体的学习主题。请重新描述您想学习的内容。",
                    "sender": "agent"
                }
        else:
            # 普通聊天消息，调用Teacher Agent处理
            # 尝试从存储的会话中获取用户背景信息
            if not user_background:
                user_background = self._get_stored_user_background(client_id)

            # 存储用户背景信息以供后续使用
            if user_background:
                self._store_user_background(client_id, user_background)

            response = await teacher.chat(client_id, message_content, user_background)

        # 添加消息ID和时间戳
        response["id"] = f"agent_{datetime.now().timestamp()}"
        if "timestamp" not in response:
            response["timestamp"] = datetime.now().isoformat()

        logger.info(f"Processed message from client {client_id}: {message_content[:50]}...")
        return response

    async def check_course_exists(self, topic: str) -> Dict[str, Any]:
        """检查课程是否已存在"""
        # 先检查记忆管理器
        course_data = self.course_memory.get_course_by_topic(topic)
        if course_data:
            logger.info(f"Course found in memory for topic: {topic}")
            return {"exists": True, "source": "memory", "course_data": course_data}

        # 再检查本地文件
        course_data = self._load_course_from_file(topic)
        if course_data:
            logger.info(f"Course found in local file for topic: {topic}")
            return {"exists": True, "source": "file", "course_data": course_data}

        logger.info(f"No existing course found for topic: {topic}")
        return {"exists": False, "source": None, "course_data": None}

    async def create_course_plan(self, topic: str, learning_goal: Optional[str] = None,
                                 duration: Optional[str] = None, background_level: Optional[str] = None) -> Dict[str, Any]:
        """
        创建课程规划
        调用CoursePlanner Agent实现，支持缓存检查
        """
        logger.info(f"AgentService.create_course_plan called with topic: {topic}")

        # 先检查是否已有课程内容
        existing_check = await self.check_course_exists(topic)
        if existing_check["exists"]:
            logger.info(f"返回已存在的课程内容，来源：{existing_check['source']}")
            # 确保缓存的课程数据也经过格式化处理
            formatted_existing = self._format_course_result(existing_check["course_data"], topic)
            return formatted_existing

        # 如果不存在，生成新的课程内容
        logger.info(f"生成新的课程内容：{topic}")
        result = await course_planner.create_course_plan(
            topic=topic,
            learning_goal=learning_goal,
            target_audience=duration,  # 将duration作为target_audience
            knowledge_level=background_level
        )

        logger.info(f"CoursePlanner returned: {result}")

        # 格式化结果
        formatted_result = self._format_course_result(result, topic)

        # 存储到记忆管理器
        try:
            course_id = self.course_memory.store_course_outline(topic, formatted_result)
            logger.info(f"Course stored in memory with ID: {course_id}")
        except Exception as e:
            logger.error(f"Error storing course in memory: {e}")

        # 保存到本地文件
        try:
            self._save_course_to_file(topic, formatted_result)
        except Exception as e:
            logger.error(f"Error saving course to file: {e}")

        return formatted_result

    def _format_course_result(self, result: Any, topic: str) -> Dict[str, Any]:
        """格式化课程结果为前端期望的格式，确保符合CourseOutlineResponse模型要求"""

        def create_default_course_structure():
            """创建默认的课程结构"""
            return {
                "title": topic + " 课程大纲",
                "chapters": [
                    {
                        "id": "1",
                        "title": "第一章：基础介绍",
                        "description": f"介绍{topic}的基本概念",
                        "sections": [
                            {"id": "1.1", "title": "概念介绍"},
                            {"id": "1.2", "title": "基础知识"}
                        ]
                    },
                    {
                        "id": "2",
                        "title": "第二章：深入学习",
                        "description": f"深入学习{topic}的核心内容",
                        "sections": [
                            {"id": "2.1", "title": "核心原理"},
                            {"id": "2.2", "title": "实际应用"}
                        ]
                    }
                ]
            }

        def validate_and_fix_chapters(chapters):
            """验证并修复章节数据结构"""
            if not isinstance(chapters, list):
                return []

            fixed_chapters = []
            for i, chapter in enumerate(chapters):
                if not isinstance(chapter, dict):
                    continue

                # 确保章节有必需的字段
                fixed_chapter = {
                    "id": str(chapter.get("id", f"chapter_{i+1}")),
                    "title": str(chapter.get("title", f"第{i+1}章")),
                    "description": str(chapter.get("description", "")),
                    "sections": []
                }

                # 处理小节
                sections = chapter.get("sections", [])
                if isinstance(sections, list):
                    for j, section in enumerate(sections):
                        if isinstance(section, dict):
                            fixed_chapter["sections"].append({
                                "id": str(section.get("id", f"{fixed_chapter['id']}.{j+1}")),
                                "title": str(section.get("title", f"第{j+1}节"))
                            })

                # 如果没有小节，创建一个默认小节
                if not fixed_chapter["sections"]:
                    fixed_chapter["sections"].append({
                        "id": f"{fixed_chapter['id']}.1",
                        "title": f"{fixed_chapter['title']} - 详细内容"
                    })

                # 保留其他可选字段
                for key in ["estimated_duration", "bloom_focus", "learning_objectives",
                           "key_concepts", "teaching_resources", "content_design_guidance",
                           "curriculum_alignment"]:
                    if key in chapter:
                        fixed_chapter[key] = chapter[key]

                fixed_chapters.append(fixed_chapter)

            return fixed_chapters

        # 检查输入结果的类型
        if not isinstance(result, dict):
            logger.error(f"CoursePlanner returned non-dict result: {type(result)}")
            return create_default_course_structure()

        logger.info(f"Formatting course result with keys: {list(result.keys())}")

        # 准备格式化后的结果
        formatted_result = {}

        # 处理标题 - 优先使用course_title，然后是title
        title = result.get("course_title") or result.get("title") or f"{topic} 课程大纲"
        formatted_result["title"] = str(title)

        # 处理章节数据
        chapters = []

        # 检查不同的数据结构
        if "chapters" in result:
            # 新格式：直接有chapters字段
            chapters = validate_and_fix_chapters(result["chapters"])

        elif "sections" in result:
            # 旧格式：有sections字段，需要转换为chapters
            sections = result.get("sections", [])
            if isinstance(sections, list):
                for section in sections:
                    if isinstance(section, dict):
                        chapter = {
                            "id": section.get("id", ""),
                            "title": section.get("title", ""),
                            "description": section.get("description", ""),
                            "sections": []
                        }

                        # 添加子节
                        if "subsections" in section and isinstance(section["subsections"], list):
                            for subsection in section["subsections"]:
                                if isinstance(subsection, dict):
                                    chapter["sections"].append({
                                        "id": str(subsection.get("id", "")),
                                        "title": str(subsection.get("title", ""))
                                    })
                        else:
                            # 如果没有子节，创建一个默认子节
                            chapter["sections"].append({
                                "id": f"{section.get('id', '1')}.1",
                                "title": str(section.get("title", "内容")) + " - 详细内容"
                            })

                        chapters.append(chapter)

            chapters = validate_and_fix_chapters(chapters)

        # 如果还是没有有效的章节，创建默认结构
        if not chapters:
            logger.warning(f"No valid chapters found in result, creating default structure")
            default_structure = create_default_course_structure()
            chapters = default_structure["chapters"]

        formatted_result["chapters"] = chapters

        # 添加其他可选字段，确保它们符合模型要求
        if "course_title" in result:
            formatted_result["course_title"] = str(result["course_title"])

        if "course_description" in result:
            formatted_result["course_description"] = str(result["course_description"])

        if "background_analysis" in result and isinstance(result["background_analysis"], dict):
            formatted_result["background_analysis"] = result["background_analysis"]

        if "bloom_taxonomy_objectives" in result and isinstance(result["bloom_taxonomy_objectives"], dict):
            formatted_result["bloom_taxonomy_objectives"] = result["bloom_taxonomy_objectives"]

        if "curriculum_alignment" in result and isinstance(result["curriculum_alignment"], dict):
            formatted_result["curriculum_alignment"] = result["curriculum_alignment"]

        logger.info(f"Successfully formatted course result with {len(chapters)} chapters")
        return formatted_result

    async def get_course_content(self, section_id: str, topic: Optional[str] = None, user_background: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        获取课程内容
        调用ContentDesigner Agent实现，支持缓存检查
        """
        logger.info(f"AgentService.get_course_content called with section_id: {section_id}")

        # 如果提供了主题，先检查本地文件缓存
        if topic:
            cached_content = self._load_section_from_file(topic, section_id)
            if cached_content:
                logger.info(f"返回缓存的章节内容：{section_id}")
                return cached_content

        # 尝试从记忆管理器获取
        memory_content = self.course_memory.get_section_content_by_id(section_id)
        if memory_content:
            logger.info(f"从记忆管理器获取章节内容：{section_id}")
            # 转换为前端期望的格式
            return self._format_section_content(memory_content, section_id)

        # 如果缓存中没有，生成新内容
        logger.info(f"生成新的章节内容：{section_id}")

        # 获取课程大纲以获取增强的章节信息
        course_outline = None
        chapter_info = None
        if topic:
            # 先从文件加载课程大纲
            course_outline = self._load_course_from_file(topic)
            logger.info(f"加载的课程大纲: {bool(course_outline)}")
            if course_outline and 'chapters' in course_outline:
                logger.info(f"课程包含 {len(course_outline['chapters'])} 个章节")
                # 查找包含当前section的chapter
                for chapter in course_outline['chapters']:
                    logger.info(f"检查章节: {chapter.get('id')} - {chapter.get('title')}")
                    if 'sections' in chapter:
                        logger.info(f"章节包含 {len(chapter['sections'])} 个小节")
                        for section in chapter['sections']:
                            logger.info(f"检查小节: {section.get('id')} vs 请求的 {section_id}")
                            if section.get('id') == section_id:
                                chapter_info = chapter
                                logger.info(f"找到匹配的章节信息: {chapter.get('title')}")
                                break
                    if chapter_info:
                        break

                if not chapter_info:
                    logger.warning(f"未找到 section_id {section_id} 对应的章节信息")

        # 解析section_id来构建章节信息
        section_parts = section_id.split('-') if '-' in section_id else section_id.split('.')
        chapter_num = section_parts[0] if section_parts else "1"
        section_num = section_parts[1] if len(section_parts) > 1 else "1"

        # 构建增强的section_info字典
        section_info = {
            "id": section_id,
            "title": f"第{chapter_num}章 第{section_num}节",
            "description": f"关于{section_id}的详细内容",
            "learning_objectives": ["理解核心概念", "掌握实际应用"],
            "key_points": ["重点内容1", "重点内容2"]
        }

        # 如果找到了对应的章节，使用其信息
        if chapter_info:
            # 查找具体的section信息
            for section in chapter_info.get('sections', []):
                if section.get('id') == section_id:
                    section_info.update({
                        "title": section.get('title', section_info['title']),
                        "content_type": section.get('content_type', '概念讲解'),
                        "activity_suggestion": section.get('activity_suggestion', '无特殊建议')
                    })
                    break

            # 从章节中继承学习目标和关键概念
            if chapter_info.get('learning_objectives'):
                section_info['learning_objectives'] = chapter_info['learning_objectives']
            if chapter_info.get('key_concepts'):
                section_info['key_points'] = chapter_info['key_concepts']

        try:
            result = await content_designer.create_content(
                section_info,
                course_topic=topic,
                user_background=user_background,
                chapter_info=chapter_info  # 传递增强的章节信息
            )
            logger.info(f"ContentDesigner returned: {result}")

            # 格式化内容
            formatted_content = self._format_content_result(result, section_info)

            # 存储到记忆管理器（如果有课程ID的话）
            try:
                # 这里需要课程ID，暂时跳过记忆存储
                # content_id = self.course_memory.store_section_content(course_id, section_info, formatted_content)
                pass
            except Exception as e:
                logger.error(f"Error storing section content in memory: {e}")

            # 保存到本地文件（如果有主题的话）
            if topic:
                try:
                    self._save_section_to_file(topic, section_id, formatted_content)
                except Exception as e:
                    logger.error(f"Error saving section content to file: {e}")

            return formatted_content

        except Exception as e:
            logger.error(f"Error calling ContentDesigner: {e}")
            # 返回默认内容以防止API失败
            return self._get_default_section_content(section_info)

    def _format_section_content(self, memory_content: Dict[str, Any], section_id: str) -> Dict[str, Any]:
        """将记忆管理器中的内容格式化为前端期望的格式"""
        # 这里需要根据记忆管理器的实际存储格式来转换
        return {
            "title": memory_content.get("title", f"章节 {section_id}"),
            "mainContent": memory_content.get("content", ""),
            "keyPoints": memory_content.get("key_points", []),
            "images": memory_content.get("images", []),
            "curriculumAlignment": memory_content.get("curriculum_alignment", [])
        }

    def _format_content_result(self, result: Any, section_info: Dict[str, Any]) -> Dict[str, Any]:
        """格式化章节内容结果"""
        # 如果 ContentDesigner 返回的是标准格式，直接使用
        if isinstance(result, dict) and all(key in result for key in ["title", "mainContent", "keyPoints"]):
            logger.info("使用 ContentDesigner 生成的增强内容")
            return result

        # 兼容旧格式（有 "content" 字段）
        if isinstance(result, dict) and "content" in result:
            # 提取内容并转换为前端期望的格式
            main_content = ""
            key_points = []
            images = []
            curriculum_alignment = []

            for item in result["content"]:
                item_type = item.get("type", "")
                if item_type == "introduction":
                    main_content += f"## 介绍\n{item.get('text', '')}\n\n"
                elif item_type == "concept":
                    main_content += f"## {item.get('title', '概念')}\n{item.get('explanation', '')}\n\n"
                    if item.get("examples"):
                        main_content += f"**示例：**\n"
                        for example in item["examples"]:
                            main_content += f"- {example}\n"
                        main_content += "\n"
                elif item_type == "activity":
                    main_content += f"## {item.get('title', '活动')}\n{item.get('description', '')}\n\n"
                    if item.get("steps"):
                        main_content += f"**步骤：**\n"
                        for i, step in enumerate(item["steps"], 1):
                            main_content += f"{i}. {step}\n"
                        main_content += "\n"
                elif item_type == "media":
                    images.append({
                        "url": "https://via.placeholder.com/600x300?text=" + item.get("title", "示例图片"),
                        "caption": item.get("description", "示例图片")
                    })
                elif item_type == "assessment":
                    for question in item.get("questions", []):
                        key_points.append(f"问题：{question.get('question', '')}")

            return {
                "title": section_info["title"],
                "mainContent": main_content or f"# {section_info['title']}\n\n这里是关于{section_info['title']}的详细内容。",
                "keyPoints": key_points or ["核心概念理解", "实际应用掌握", "相关知识点整合"],
                "images": images,
                "curriculumAlignment": curriculum_alignment or ["符合课程标准要求", "对应学习目标", "适合目标年龄段"]
            }
        else:
            logger.warning("使用默认内容，因为 ContentDesigner 返回了无效格式")
            return self._get_default_section_content(section_info)

    def _get_default_section_content(self, section_info: Dict[str, Any]) -> Dict[str, Any]:
        """获取默认的章节内容"""
        return {
            "title": section_info["title"],
            "mainContent": f"# {section_info['title']}\n\n这里是关于{section_info['title']}的详细内容。\n\n## 主要内容\n\n本节将介绍相关的核心概念和实际应用。",
            "keyPoints": ["核心概念理解", "实际应用掌握", "相关知识点整合"],
            "images": [],
            "curriculumAlignment": ["符合课程标准要求", "对应学习目标", "适合目标年龄段"]
        }

    async def get_user_progress(self) -> Dict[str, Any]:
        """
        获取用户学习进度
        这将由LearningProfiler Agent实现
        """
        # 临时模拟返回数据，后续将实现实际的Agent调用
        progress = {
            "completedChapters": ["1", "2"],
            "achievements": [
                {"id": "1", "title": "学习初探", "description": "完成第一章学习"},
                {"id": "2", "title": "学习进阶", "description": "完成两个章节学习"}
            ],
            "progressPercentage": 60
        }
        logger.info("Retrieved user progress")
        return progress

    def _extract_user_background(self, message_content: str) -> Optional[Dict[str, Any]]:
        """
        从消息内容中提取用户背景信息
        """
        try:
            # 查找用户背景信息标记
            if "## 用户背景信息" in message_content:
                lines = message_content.split('\n')
                background_info = {}

                for line in lines:
                    line = line.strip()
                    if line.startswith("年龄/年级:"):
                        background_info["age"] = line.split(":", 1)[1].strip()
                    elif line.startswith("学习目标:"):
                        background_info["learningGoal"] = line.split(":", 1)[1].strip()
                    elif line.startswith("时间偏好:"):
                        background_info["timePreference"] = line.split(":", 1)[1].strip()
                    elif line.startswith("知识水平:"):
                        background_info["knowledgeLevel"] = line.split(":", 1)[1].strip()
                    elif line.startswith("目标受众:"):
                        background_info["targetAudience"] = line.split(":", 1)[1].strip()

                # 如果没有显式的知识水平信息，根据年龄推断
                if "knowledgeLevel" not in background_info and "age" in background_info:
                    age_info = background_info["age"].lower()
                    if "幼儿" in age_info or "幼" in age_info:
                        background_info["knowledgeLevel"] = "启蒙级"
                    elif "小学" in age_info or "1年级" in age_info or "2年级" in age_info or "3年级" in age_info:
                        background_info["knowledgeLevel"] = "初学者"
                    elif "4年级" in age_info or "5年级" in age_info or "6年级" in age_info:
                        background_info["knowledgeLevel"] = "入门级"
                    elif "初中" in age_info or "7年级" in age_info or "8年级" in age_info or "9年级" in age_info:
                        background_info["knowledgeLevel"] = "基础级"
                    elif "高中" in age_info or "高一" in age_info or "高二" in age_info or "高三" in age_info:
                        background_info["knowledgeLevel"] = "进阶级"
                    else:
                        background_info["knowledgeLevel"] = "初学者"

                return background_info if background_info else None

        except Exception as e:
            logger.error(f"Error extracting user background: {e}")

        return None

    def _store_user_background(self, client_id: str, user_background: Dict[str, Any]) -> None:
        """
        存储用户背景信息
        """
        self.user_backgrounds[client_id] = user_background
        logger.info(f"Stored user background for client {client_id}: {user_background}")

    def _get_stored_user_background(self, client_id: str) -> Optional[Dict[str, Any]]:
        """
        获取存储的用户背景信息
        """
        background = self.user_backgrounds.get(client_id)
        if background:
            logger.info(f"Retrieved stored user background for client {client_id}")
        return background

    def _is_course_planning_request(self, message_content: str) -> bool:
        """
        判断是否是课程规划请求
        """
        # 检查是否包含课程规划相关的标识
        planning_indicators = [
            "## Agent处理指令",
            "CoursePlanner",
            "ContentGenerator",
            "Monitor",
            "Verifier"
        ]

        for indicator in planning_indicators:
            if indicator in message_content:
                return True

        return False

    def _extract_original_topic(self, message_content: str) -> Optional[str]:
        """
        从格式化消息中提取原始学习主题
        """
        try:
            # 消息内容的第一行通常是原始主题
            lines = message_content.split('\n')
            for line in lines:
                line = line.strip()
                if line and not line.startswith("#") and not line.startswith("年龄") and not line.startswith("学习目标"):
                    return line
        except Exception as e:
            logger.error(f"Error extracting original topic: {e}")

        return None

    async def create_course_plan_with_background(self, topic: str, user_background: Dict[str, Any]) -> Dict[str, Any]:
        """
        使用用户背景信息创建个性化课程规划
        """
        logger.info(f"Creating personalized course plan for topic: {topic} with background: {user_background}")

        # 调用CoursePlanner Agent，传入用户背景信息作为独立参数
        result = await course_planner.create_course_plan(
            topic=topic,
            learning_goal=user_background.get("learningGoal"),
            target_audience=user_background.get("age"),  # 使用年龄作为目标受众
            knowledge_level=user_background.get("knowledgeLevel"),
            store_to_memory=True
        )

        # 存储用户背景信息到结果中，以便后续Agent使用
        if isinstance(result, dict):
            result["user_background"] = user_background
            result["personalization_applied"] = True

        # 格式化结果
        formatted_result = self._format_course_result(result, topic)

        # 保存包含用户背景信息的课程到本地文件
        try:
            formatted_result["user_background"] = user_background
            self._save_course_to_file(topic, formatted_result)
        except Exception as e:
            logger.error(f"Error saving personalized course to file: {e}")

        return formatted_result

# 创建一个全局的AgentService实例
agent_service = AgentService()
