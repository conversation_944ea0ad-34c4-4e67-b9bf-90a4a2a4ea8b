# AI教学系统环境变量配置文件
# 复制此文件为 .env 并填入真实的API密钥

# =============================================================================
# 模型提供商API密钥配置
# =============================================================================

# OpenAI 配置 (GPT-4, GPT-3.5等)
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_BASE_URL=https://api.openai.com/v1
OPENAI_ORG_ID=your_org_id_here

# xAI 配置 (Grok)
XAI_API_KEY=your_xai_api_key_here
XAI_BASE_URL=https://api.x.ai/v1

# Google Gemini 配置
GOOGLE_API_KEY=your_google_api_key_here
GEMINI_API_KEY=your_google_api_key_here

# Anthropic Claude 配置
ANTHROPIC_API_KEY=your_anthropic_api_key_here
CLAUDE_API_KEY=your_anthropic_api_key_here

# Azure OpenAI 配置
AZURE_OPENAI_API_KEY=your_azure_openai_key_here
AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com/
AZURE_OPENAI_API_VERSION=2024-02-15-preview

# Ollama 本地配置
OLLAMA_HOST=http://localhost:11434

# =============================================================================
# 应用程序配置
# =============================================================================

# 运行环境
ENVIRONMENT=development

# 数据库配置
DATABASE_URL=sqlite:///memory/teaching_memory.db

# 日志级别
LOG_LEVEL=INFO

# 模型配置文件路径
MODEL_CONFIG_PATH=config/models.yaml
