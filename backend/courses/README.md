# 课程存储目录

这个目录用于存储生成的课程内容，包括课程大纲和章节内容。

## 文件命名规则

### 课程大纲文件
- 格式：`course_{topic_id}.json`
- 示例：`course_a1b2c3d4e5f6.json`
- 内容：完整的课程大纲，包括章节结构

### 章节内容文件
- 格式：`section_{topic_id}_{section_id}.json`
- 示例：`section_a1b2c3d4e5f6_1_1.json`
- 内容：特定章节的详细内容

## 文件结构

### 课程大纲文件结构
```json
{
  "title": "课程标题",
  "topic": "原始主题",
  "chapters": [
    {
      "id": "章节ID",
      "title": "章节标题",
      "description": "章节描述",
      "sections": [
        {
          "id": "小节ID",
          "title": "小节标题"
        }
      ]
    }
  ],
  "saved_at": "2024-01-01T00:00:00.000Z"
}
```

### 章节内容文件结构
```json
{
  "title": "章节标题",
  "topic": "原始主题",
  "section_id": "章节ID",
  "mainContent": "主要内容（Markdown格式）",
  "keyPoints": ["关键点1", "关键点2"],
  "images": [
    {
      "url": "图片URL",
      "caption": "图片说明"
    }
  ],
  "curriculumAlignment": ["课标对齐1", "课标对齐2"],
  "saved_at": "2024-01-01T00:00:00.000Z"
}
```

## 功能特性

1. **自动持久化**：课程生成后自动保存到本地文件
2. **缓存机制**：优先从本地文件加载，避免重复生成
3. **版本管理**：每个文件包含保存时间戳
4. **唯一标识**：通过主题hash生成唯一ID，避免重复

## 注意事项

- 请勿手动删除或修改这些文件，除非您知道自己在做什么
- 文件会随着课程生成自动创建和更新
- 如果需要重新生成课程，可以删除对应的文件 