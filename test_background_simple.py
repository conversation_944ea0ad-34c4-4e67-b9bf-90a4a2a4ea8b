#!/usr/bin/env python3
"""测试背景信息显示修复"""

import asyncio
import json
import sys
import os

# 添加项目路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend/src'))

async def test_background_fix():
    """简单测试背景信息修复"""
    print("🧪 测试背景信息修复...")
    
    try:
        from services.agent_service import AgentService
        
        # 测试数据
        user_background = {
            "age": "初出茅庐(3~5年级)",
            "learningGoal": "系统学习",
            "timePreference": "几周计划",
            "knowledgeLevel": "初步抽象思维形成"
        }
        
        topic = "小学数学分数"
        
        print(f"📋 测试: {topic}")
        print(f"用户年龄: {user_background['age']}")
        
        # 调用Agent
        agent_service = AgentService()
        result = await agent_service.create_course_plan_with_background(
            topic=topic,
            user_background=user_background
        )
        
        # 检查结果
        if 'background_analysis' in result:
            bg = result['background_analysis']
            target_age = bg.get('target_age', '')
            
            print(f"\n📊 生成的年龄信息: {target_age}")
            
            # 验证是否包含正确的年龄信息
            if any(x in target_age for x in ['3', '4', '5', '年级', '小学']):
                print("✅ 年龄信息正确!")
                return True
            elif '初中' in target_age or '12-14' in target_age:
                print("❌ 仍然生成错误年龄信息")
                return False
            else:
                print(f"⚠️ 年龄信息需要检查: {target_age}")
                return False
        else:
            print("❌ 缺少背景分析")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_background_fix())
