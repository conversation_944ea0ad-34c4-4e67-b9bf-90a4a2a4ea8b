#!/usr/bin/env python3
"""
实际网站测试指南 - 用户背景信息传递验证
"""

print("""
🌐 实际网站测试指南 - 用户背景信息传递验证
=====================================================

✅ 服务状态确认:
   - 前端: http://localhost:8080 
   - 后端: http://localhost:8000

🎯 测试流程:

1️⃣ 【首页用户背景信息收集测试】
   - 打开 http://localhost:8080
   - 在输入框中输入: "我想学习小学数学中的分数知识"
   - 点击"去探索"按钮
   - 验证弹出背景信息收集弹窗

2️⃣ 【用户背景信息选择测试】
   步骤1: 年龄选择
   - 选择 "初出茅庐(3~5年级)"
   - 点击"下一步"
   
   步骤2: 学习目标选择  
   - 选择 "系统学习 - 全面掌握知识体系"
   - 点击"下一步"
   
   步骤3: 时间偏好选择
   - 选择 "🗓️ 几周的学习计划"
   - 点击"开始生成课程"

3️⃣ 【页面跳转和课程生成测试】
   - 验证自动跳转到 /course-planning 页面
   - 观察是否显示"正在生成个性化课程..."
   - 等待Agent回复

4️⃣ 【背景信息使用验证】
   检查Agent回复是否包含以下关键信息:
   ✅ 提到"初出茅庐(3~5年级)"或"小学"
   ✅ 提到"系统学习"或"全面掌握"  
   ✅ 提到"几周"或时间规划
   ✅ 内容难度适合小学3-5年级水平
   ✅ 课程结构体现系统性学习

5️⃣ 【多场景测试】
   重复测试不同背景信息组合:
   
   场景A: 幼儿园学生
   - 年龄: "蓬头稚子(K-2年级)"
   - 目标: "满足好奇心 - 快速了解基础概念"
   - 时间: "⏱️ 几小时的学习计划"
   - 主题: "我想了解简单的加减法"
   
   场景B: 中学生
   - 年龄: "初露锋芒(6-8年级)" 
   - 目标: "系统学习 - 全面掌握知识体系"
   - 时间: "🌖 几天的计划"
   - 主题: "我想学习初中物理力学"
   
   场景C: 高中生
   - 年龄: "日臻成熟(9-12年级)"
   - 目标: "满足好奇心 - 快速了解基础概念"  
   - 时间: "⏱️ 几小时的学习计划"
   - 主题: "我想了解高中化学有机化合物"

🔍 【验证要点】:

✅ 前端数据收集:
   - 三步问题弹窗正常显示
   - 用户选择能正确保存
   - 页面跳转流畅

✅ 数据传递机制:
   - WebSocket连接建立成功
   - 格式化消息包含完整背景信息
   - 后端Agent能接收到背景信息

✅ Agent个性化处理:
   - 回复内容体现用户年龄特征
   - 教学方法匹配学习目标
   - 课程安排符合时间偏好
   - 内容深度适合知识水平

✅ 用户体验:
   - 界面响应流畅
   - 加载状态清晰
   - 个性化效果明显

📊 【成功标准】:
1. 所有测试场景都能收到Agent回复
2. 回复内容明确体现用户背景信息
3. 个性化程度满足预期
4. 前端界面交互正常
5. 没有错误或异常情况

⚠️ 【常见问题排查】:
- 如果WebSocket连接失败: 检查后端服务状态
- 如果Agent无回复: 检查Ollama模型状态
- 如果背景信息未体现: 检查消息格式化逻辑
- 如果页面跳转异常: 检查路由配置

🎉 【测试完成标志】:
当所有场景测试通过，且Agent回复都能体现相应的用户背景信息时，
可以确认用户背景信息传递机制完全正常工作！

=====================================================
现在请在浏览器中按照上述流程进行实际测试 🚀
""")
