#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试背景信息显示修复
验证Course Planner Agent是否正确使用用户提供的背景信息，而不是自行推断
"""

import asyncio
import json
import logging
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend/src'))

from services.agent_service import AgentService

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_background_info_fix():
    """测试背景信息修复是否有效"""
    print("🧪 开始测试背景信息显示修复...")
    
    # 创建测试用的用户背景信息
    test_user_background = {
        "age": "初出茅庐(3~5年级)",  # 用户明确选择的年龄
        "learningGoal": "系统学习 - 全面掌握知识体系",
        "timePreference": "🗓️ 几周的学习计划",
        "knowledgeLevel": "初步抽象思维形成"  # 根据年龄推断的知识水平
    }
    
    test_topic = "我想学习小学数学分数"
    
    print(f"📋 测试参数:")
    print(f"  主题: {test_topic}")
    print(f"  用户背景: {json.dumps(test_user_background, ensure_ascii=False, indent=2)}")
    
    try:
        # 创建AgentService实例
        agent_service = AgentService()
        
        # 使用修复后的Course Planner Agent生成课程
        print("\n🚀 调用Course Planner Agent...")
        result = await agent_service.create_course_plan_with_background(
            topic=test_topic,
            user_background=test_user_background
        )
        
        # 验证结果
        print("\n✅ Course Planner Agent响应成功!")
        
        # 检查background_analysis字段
        if 'background_analysis' in result:
            bg_analysis = result['background_analysis']
            print(f"\n📊 生成的背景分析:")
            print(json.dumps(bg_analysis, ensure_ascii=False, indent=2))
            
            # 验证年龄信息是否正确
            target_age = bg_analysis.get('target_age', '')
            knowledge_level = bg_analysis.get('knowledge_level', '')
            
            print(f"\n🔍 验证结果:")
            
            # 检查年龄信息
            if any(keyword in target_age for keyword in ['3', '4', '5', '年级', '小学']):
                print(f"✅ 年龄信息正确: {target_age}")
                age_correct = True
            else:
                print(f"❌ 年龄信息错误: 期望包含'3-5年级'或'小学'相关信息，实际: {target_age}")
                age_correct = False
            
            # 检查知识水平
            if '初步抽象思维' in knowledge_level or '初学' in knowledge_level or '基础' in knowledge_level:
                print(f"✅ 知识水平合理: {knowledge_level}")
                knowledge_correct = True
            else:
                print(f"⚠️  知识水平需要检查: {knowledge_level}")
                knowledge_correct = True  # 知识水平可以有一定变化，只要不是明显错误即可
            
            # 检查是否包含错误的年龄信息（如初中阶段）
            if '初中' in target_age or '12-14' in target_age:
                print(f"❌ 检测到错误的年龄推断: {target_age}")
                age_correct = False
            
            if age_correct and knowledge_correct:
                print("\n🎉 背景信息修复成功! Agent正确使用了用户提供的背景信息")
                return True
            else:
                print("\n❌ 背景信息修复失败，Agent仍在自行推断信息")
                return False
                
        else:
            print("❌ 结果中缺少background_analysis字段")
            return False
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        print(f"❌ 测试失败: {e}")
        return False

async def test_multiple_age_groups():
    """测试多个年龄组，确保修复对所有年龄组都有效"""
    print("\n🧪 测试多个年龄组...")
    
    test_cases = [
        {
            "age": "蓬头稚子(K-2)",
            "expected_keywords": ["幼儿", "K", "1", "2", "年级", "儿童"]
        },
        {
            "age": "初出茅庐(3~5年级)", 
            "expected_keywords": ["3", "4", "5", "年级", "小学"]
        },
        {
            "age": "初露锋芒(6-8年级)",
            "expected_keywords": ["6", "7", "8", "年级", "小学", "初中"]
        },
        {
            "age": "日臻成熟(9-12年级)",
            "expected_keywords": ["9", "10", "11", "12", "年级", "初中", "高中"]
        }
    ]
    
    agent_service = AgentService()
    all_passed = True
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n--- 测试案例 {i}: {test_case['age']} ---")
        
        user_background = {
            "age": test_case["age"],
            "learningGoal": "快速了解 - 满足好奇心",
            "timePreference": "⏱️ 几小时的学习计划",
            "knowledgeLevel": "待评估"
        }
        
        try:
            result = await agent_service.create_course_plan_with_background(
                topic="基础数学概念",
                user_background=user_background
            )
            
            if 'background_analysis' in result:
                target_age = result['background_analysis'].get('target_age', '')
                print(f"生成的年龄信息: {target_age}")
                
                # 检查是否包含期望的关键词
                found_keywords = [kw for kw in test_case['expected_keywords'] if kw in target_age]
                if found_keywords:
                    print(f"✅ 找到匹配关键词: {found_keywords}")
                else:
                    print(f"❌ 未找到匹配关键词，期望: {test_case['expected_keywords']}")
                    all_passed = False
            else:
                print("❌ 缺少background_analysis")
                all_passed = False
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            all_passed = False
    
    return all_passed

async def main():
    """主测试函数"""
    print("🔧 背景信息显示修复测试")
    print("=" * 50)
    
    # 基础修复测试
    basic_test_passed = await test_background_info_fix()
    
    # 多年龄组测试
    multi_test_passed = await test_multiple_age_groups()
    
    print("\n" + "=" * 50)
    print("📋 测试总结:")
    print(f"基础修复测试: {'✅ 通过' if basic_test_passed else '❌ 失败'}")
    print(f"多年龄组测试: {'✅ 通过' if multi_test_passed else '❌ 失败'}")
    
    if basic_test_passed and multi_test_passed:
        print("\n🎉 所有测试通过! 背景信息显示修复成功")
        print("\n📋 修复说明:")
        print("- Course Planner Agent现在优先使用用户提供的背景信息")
        print("- background_analysis中的target_age字段现在反映用户实际选择")
        print("- UI显示的背景信息将与用户输入保持一致")
    else:
        print("\n❌ 部分测试失败，需要进一步检查修复")

if __name__ == "__main__":
    asyncio.run(main())
