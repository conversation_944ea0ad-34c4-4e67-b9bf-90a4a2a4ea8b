<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程选择对话框流程测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-title {
            font-size: 18px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .test-description {
            color: #666;
            margin-bottom: 15px;
        }
        .test-button {
            background: #0066cc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin-right: 10px;
            text-decoration: none;
            display: inline-block;
        }
        .test-button:hover {
            background: #0052a3;
        }
        .status {
            margin-top: 15px;
            padding: 10px;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>课程选择对话框流程测试</h1>
    <p>此测试页面用于验证CourseSelectionDialog只在用户从首页提交主题后才会触发。</p>

    <div class="test-section">
        <div class="test-title">测试场景 1: 正常流程（应该显示对话框）</div>
        <div class="test-description">
            1. 在首页输入主题<br>
            2. 填写背景信息<br>
            3. 点击"去探索"按钮<br>
            4. 页面跳转到课程规划页面<br>
            <strong>预期结果：应该显示CourseSelectionDialog</strong>
        </div>
        <a href="http://localhost:8080" class="test-button" target="_blank">打开首页进行测试</a>
        <div id="test1-status" class="status info" style="display: none;">
            请按照上述步骤在新标签页中进行测试
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景 2: 直接访问URL（不应该显示对话框）</div>
        <div class="test-description">
            直接访问/course-planning页面，而不是从首页跳转<br>
            <strong>预期结果：不应该显示CourseSelectionDialog，如果没有缓存课程则提示返回首页</strong>
        </div>
        <a href="http://localhost:8080/course-planning" class="test-button" target="_blank">直接访问课程规划页面</a>
        <div id="test2-status" class="status info" style="display: none;">
            请在新标签页中检查是否显示了对话框
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景 3: 清除localStorage后直接访问</div>
        <div class="test-description">
            清除localStorage中的课程缓存，然后直接访问课程规划页面<br>
            <strong>预期结果：应该重定向到首页或显示错误提示</strong>
        </div>
        <button class="test-button" onclick="clearStorage()">清除缓存</button>
        <a href="http://localhost:8080/course-planning" class="test-button" target="_blank">访问课程规划页面</a>
        <div id="test3-status" class="status info" style="display: none;">
            localStorage已清除，请在新标签页中检查行为
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">测试场景 4: 页面刷新后的行为</div>
        <div class="test-description">
            在课程规划页面刷新浏览器<br>
            <strong>预期结果：不应该重新显示CourseSelectionDialog，如果有缓存则直接加载课程</strong>
        </div>
        <div id="test4-status" class="status info">
            请在完成场景1测试后，在课程规划页面刷新浏览器进行此测试
        </div>
    </div>

    <div class="test-section">
        <div class="test-title">API状态检查</div>
        <div class="test-description">检查后端API服务是否正常运行</div>
        <button class="test-button" onclick="checkAPI()">检查API状态</button>
        <div id="api-status"></div>
    </div>

    <script>
        function clearStorage() {
            localStorage.clear();
            document.getElementById('test3-status').style.display = 'block';
            document.getElementById('test3-status').textContent = 'localStorage已清除，请在新标签页中检查行为';
        }

        async function checkAPI() {
            const statusDiv = document.getElementById('api-status');
            statusDiv.style.display = 'block';
            statusDiv.className = 'status info';
            statusDiv.textContent = '正在检查API状态...';

            try {
                // 检查健康状态
                const healthResponse = await fetch('http://localhost:8000/health');
                if (!healthResponse.ok) {
                    throw new Error('健康检查失败');
                }

                // 检查课程列表API
                const listResponse = await fetch('http://localhost:8000/api/course/list');
                if (!listResponse.ok) {
                    throw new Error('课程列表API失败');
                }
                const listData = await listResponse.json();

                statusDiv.className = 'status success';
                statusDiv.innerHTML = `
                    ✅ API服务正常运行<br>
                    📋 已存储课程数量: ${listData.courses?.length || 0}
                `;
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = `❌ API检查失败: ${error.message}`;
            }
        }

        // 页面加载时检查API状态
        window.addEventListener('load', checkAPI);
    </script>
</body>
</html>
