#!/usr/bin/env python3
"""
前端集成测试脚本
测试用户背景信息从前端到后端的完整数据流
"""

import asyncio
import websockets
import json
import time
from datetime import datetime


class FrontendIntegrationTester:
    def __init__(self):
        self.websocket_url = "ws://localhost:8000/api/ws/chat"
        self.client_id = f"test_client_{int(time.time())}"
        
    async def simulate_frontend_flow(self):
        """模拟完整的前端用户流程"""
        
        print("🚀 开始前端集成测试")
        print("=" * 60)
        
        # 1. 模拟用户在前端选择背景信息
        user_background = {
            "age": "初出茅庐(3~5年级)",
            "learningGoal": "系统学习 - 全面掌握知识体系", 
            "timePreference": "🗓️ 几周的学习计划",
            "knowledgeLevel": "初步抽象思维形成",
            "targetAudience": "初出茅庐(3~5年级)"
        }
        
        user_topic = "我想学习小学数学中的分数知识"
        
        print("📝 用户背景信息:")
        for key, value in user_background.items():
            print(f"   {key}: {value}")
        print(f"\n📋 用户主题: {user_topic}")
        
        # 2. 模拟前端createFormattedPrompt的逻辑
        formatted_message = self.create_formatted_prompt(user_topic, user_background)
        
        print("\n📤 格式化后的消息预览:")
        print("-" * 40)
        print(formatted_message[:300] + "..." if len(formatted_message) > 300 else formatted_message)
        print("-" * 40)
        
        # 3. 通过WebSocket发送消息（模拟前端WebSocket发送）
        try:
            await self.test_websocket_communication(formatted_message)
        except Exception as e:
            print(f"❌ WebSocket通信测试失败: {e}")
            return False
            
        return True
    
    def create_formatted_prompt(self, original_prompt: str, background: dict) -> str:
        """模拟前端ChatContext.createFormattedPrompt方法"""
        
        formatted = f"""{original_prompt}

## 用户背景信息
年龄/年级: {background['age']}
学习目标: {background['learningGoal']}
时间偏好: {background['timePreference']}
知识水平: {background.get('knowledgeLevel', '')}
目标受众: {background.get('targetAudience', '')}

## Agent处理指令
请所有后续Agent (CoursePlanner, ContentGenerator, Monitor, Verifier等) 在处理时考虑以上背景信息：
1. 根据年龄/年级调整内容难度和教学方法
2. 对齐学习目标，确保课程设计符合用户期望
3. 考虑时间约束，合理安排学习进度和内容密度
4. 在内容验证和监督过程中应用这些背景信息作为评估标准"""

        return formatted
    
    async def test_websocket_communication(self, message: str):
        """测试WebSocket通信"""
        
        print(f"\n🔌 连接WebSocket: {self.websocket_url}/{self.client_id}")
        
        try:
            async with websockets.connect(f"{self.websocket_url}/{self.client_id}") as websocket:
                print("✅ WebSocket连接成功")
                
                # 模拟前端发送JSON格式消息
                message_data = {
                    "content": message,
                    "sender": "user"
                }
                
                print("📤 发送消息到后端...")
                await websocket.send(json.dumps(message_data))
                
                print("⏳ 等待Agent回复...")
                
                # 等待回复（设置超时）
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=60.0)
                    response_data = json.loads(response)
                    
                    print("✅ 收到Agent回复!")
                    print(f"📨 回复发送者: {response_data.get('sender', 'unknown')}")
                    print(f"📨 回复长度: {len(response_data.get('content', ''))}")
                    
                    # 检查回复内容是否包含背景信息相关的关键词
                    content = response_data.get('content', '').lower()
                    
                    background_keywords = [
                        "小学", "3年级", "4年级", "5年级", 
                        "分数", "系统学习", "几周",
                        "初步抽象思维", "初出茅庐"
                    ]
                    
                    found_keywords = [kw for kw in background_keywords if kw in content]
                    
                    print(f"\n🔍 背景信息关键词检测:")
                    print(f"   找到的关键词: {found_keywords}")
                    print(f"   关键词匹配率: {len(found_keywords)}/{len(background_keywords)} ({len(found_keywords)/len(background_keywords)*100:.1f}%)")
                    
                    if len(found_keywords) >= 3:
                        print("✅ Agent成功使用了用户背景信息!")
                    else:
                        print("⚠️ Agent可能没有充分使用用户背景信息")
                    
                    # 显示回复内容的前500字符
                    print(f"\n📄 回复内容预览:")
                    print("-" * 40)
                    preview = response_data.get('content', '')[:500]
                    print(preview + "..." if len(response_data.get('content', '')) > 500 else preview)
                    print("-" * 40)
                    
                    return True
                    
                except asyncio.TimeoutError:
                    print("❌ 等待回复超时 (60秒)")
                    return False
                    
        except Exception as e:
            print(f"❌ WebSocket连接失败: {e}")
            return False
    
    async def test_multiple_scenarios(self):
        """测试多个场景"""
        
        scenarios = [
            {
                "name": "小学生数学场景",
                "background": {
                    "age": "初出茅庐(3~5年级)",
                    "learningGoal": "系统学习 - 全面掌握知识体系",
                    "timePreference": "🗓️ 几周的学习计划",
                    "knowledgeLevel": "初步抽象思维形成",
                    "targetAudience": "初出茅庐(3~5年级)"
                },
                "topic": "我想学习小学数学中的分数知识"
            },
            {
                "name": "中学生物理场景", 
                "background": {
                    "age": "初露锋芒(6-8年级)",
                    "learningGoal": "满足好奇心 - 快速了解基础概念",
                    "timePreference": "⏱️ 几小时的学习计划",
                    "knowledgeLevel": "抽象和逻辑能力提升",
                    "targetAudience": "初露锋芒(6-8年级)"
                },
                "topic": "我想了解物理中的力学基础"
            },
            {
                "name": "高中生化学场景",
                "background": {
                    "age": "日臻成熟(9-12年级)",
                    "learningGoal": "系统学习 - 全面掌握知识体系",
                    "timePreference": "🌖 几天的计划", 
                    "knowledgeLevel": "系统思维逐步建立",
                    "targetAudience": "日臻成熟(9-12年级)"
                },
                "topic": "我想学习高中化学中的有机化学"
            }
        ]
        
        print("🎯 开始多场景测试")
        print("=" * 60)
        
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n📋 场景 {i}: {scenario['name']}")
            print("-" * 30)
            
            # 重新生成客户端ID避免冲突
            self.client_id = f"test_client_{int(time.time())}_{i}"
            
            formatted_message = self.create_formatted_prompt(
                scenario['topic'], 
                scenario['background']
            )
            
            try:
                success = await self.test_websocket_communication(formatted_message)
                results.append({
                    'scenario': scenario['name'],
                    'success': success
                })
                
                if success:
                    print(f"✅ 场景 {i} 测试通过")
                else:
                    print(f"❌ 场景 {i} 测试失败")
                    
            except Exception as e:
                print(f"❌ 场景 {i} 执行异常: {e}")
                results.append({
                    'scenario': scenario['name'],
                    'success': False
                })
            
            # 场景间等待，避免并发冲突
            if i < len(scenarios):
                print("⏳ 等待5秒后继续下一个场景...")
                await asyncio.sleep(5)
        
        # 总结测试结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        success_count = sum(1 for r in results if r['success'])
        total_count = len(results)
        
        for result in results:
            status = "✅ 通过" if result['success'] else "❌ 失败"
            print(f"   {result['scenario']}: {status}")
        
        print(f"\n总体成功率: {success_count}/{total_count} ({success_count/total_count*100:.1f}%)")
        
        if success_count == total_count:
            print("🎉 所有测试场景都通过! 前端集成测试成功!")
        elif success_count > 0:
            print("⚠️ 部分测试场景通过，需要进一步检查失败的场景")
        else:
            print("❌ 所有测试场景都失败，前端集成存在问题")


async def main():
    """主函数"""
    
    print("🔧 前端用户背景信息传递机制集成测试")
    print("测试目标: 验证用户背景信息从前端到后端的完整数据流")
    print("=" * 60)
    
    tester = FrontendIntegrationTester()
    
    try:
        # 首先进行单场景测试
        print("1️⃣ 单场景基础测试")
        await tester.simulate_frontend_flow()
        
        print("\n" + "=" * 60)
        
        # 然后进行多场景测试  
        print("2️⃣ 多场景完整测试")
        await tester.test_multiple_scenarios()
        
    except Exception as e:
        print(f"❌ 测试执行异常: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    asyncio.run(main())
