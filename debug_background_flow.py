#!/usr/bin/env python3
"""
调试用户背景信息数据流的完整跟踪脚本
确认用户背景信息是否在所有Agent中得到正确传递和使用
"""

import asyncio
import json
import sys
import os
from typing import Dict, Any, Optional

# 添加backend/src到路径
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend', 'src'))

from services.agent_service import agent_service
from agents.teaching_team.course_planner import course_planner
from agents.teaching_team.content_designer import content_designer

# 测试用的用户背景信息
TEST_USER_BACKGROUND = {
    "age": "小学4年级",
    "learningGoal": "提升科学探究能力", 
    "timePreference": "短时集中学习",
    "knowledgeLevel": "初学者"
}

# 测试用的格式化消息（模拟前端发送的消息）
TEST_FORMATTED_MESSAGE = """我想学习《光的传播》

## 用户背景信息
年龄/年级: 小学4年级
学习目标: 提升科学探究能力
时间偏好: 短时集中学习

## Agent处理指令
请根据上述用户背景信息，为用户生成个性化的《光的传播》课程规划。

- CoursePlanner: 根据小学4年级的认知水平和科学探究目标，设计合适的课程大纲
- ContentGenerator: 为每个章节生成符合短时集中学习特点的教学内容
- Monitor: 确保内容难度适合初学者，突出科学探究方法
- Verifier: 验证课程内容是否与用户背景信息完全匹配"""

async def test_user_background_extraction():
    """测试用户背景信息提取"""
    print("🔍 测试1: 用户背景信息提取")
    print("=" * 50)
    
    # 测试agent_service的背景信息提取
    extracted_background = agent_service._extract_user_background(TEST_FORMATTED_MESSAGE)
    
    print(f"📥 输入消息预览: {TEST_FORMATTED_MESSAGE[:100]}...")
    print(f"📤 提取的背景信息: {json.dumps(extracted_background, ensure_ascii=False, indent=2)}")
    
    # 验证提取是否正确
    if extracted_background:
        expected_keys = ["age", "learningGoal", "timePreference"]
        missing_keys = [key for key in expected_keys if key not in extracted_background]
        if missing_keys:
            print(f"❌ 缺失的背景信息字段: {missing_keys}")
            return False
        else:
            print("✅ 背景信息提取成功，包含所有必要字段")
            return True
    else:
        print("❌ 背景信息提取失败")
        return False

async def test_course_planner_background_usage():
    """测试CoursePlanner是否正确使用用户背景信息"""
    print("\n🎯 测试2: CoursePlanner背景信息使用")
    print("=" * 50)
    
    topic = "光的传播"
    
    try:
        # 直接调用course_planner，传入用户背景信息
        result = await course_planner.create_course_plan(
            topic=topic,
            learning_goal=TEST_USER_BACKGROUND["learningGoal"],
            target_audience=TEST_USER_BACKGROUND["age"], 
            knowledge_level=TEST_USER_BACKGROUND.get("knowledgeLevel", "初学者"),
            store_to_memory=False  # 测试时不存储
        )
        
        print(f"📤 CoursePlanner返回结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"📋 课程标题: {result.get('course_title', result.get('title', '未找到'))}")
            
            # 检查是否包含背景分析
            if 'background_analysis' in result:
                bg_analysis = result['background_analysis']
                print(f"📊 背景分析: {json.dumps(bg_analysis, ensure_ascii=False, indent=2)}")
                
                # 验证背景分析是否符合输入的用户背景
                target_age = bg_analysis.get('target_age', '').lower()
                if '4年级' in target_age or '小学' in target_age:
                    print("✅ 年龄信息正确体现")
                else:
                    print(f"❌ 年龄信息不匹配，期望包含'4年级'或'小学'，实际: {target_age}")
                    return False
            else:
                print("❌ 结果中缺少background_analysis字段")
                return False
            
            # 检查章节设计是否适合目标年龄
            if 'chapters' in result:
                chapters = result['chapters']
                print(f"📚 章节数量: {len(chapters)}")
                
                # 查看第一个章节的详细信息
                if chapters:
                    first_chapter = chapters[0]
                    print(f"📖 第一章节: {first_chapter.get('title', '未找到')}")
                    
                    # 检查内容设计指导
                    if 'content_design_guidance' in first_chapter:
                        guidance = first_chapter['content_design_guidance']
                        difficulty = guidance.get('difficulty_level', '').lower()
                        print(f"🎯 难度等级: {difficulty}")
                        
                        if '初级' in difficulty or '小学' in difficulty or '基础' in difficulty:
                            print("✅ 难度等级符合小学4年级水平")
                        else:
                            print(f"❌ 难度等级可能不适合，实际: {difficulty}")
                            return False
                    else:
                        print("⚠️  章节缺少content_design_guidance字段")
                        
            print("✅ CoursePlanner正确使用了用户背景信息")
            return True
        else:
            print(f"❌ CoursePlanner返回了非字典类型: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ CoursePlanner调用失败: {e}")
        return False

async def test_content_designer_background_usage():
    """测试ContentDesigner是否正确使用用户背景信息"""
    print("\n📝 测试3: ContentDesigner背景信息使用")
    print("=" * 50)
    
    # 构造测试用的章节信息
    section_info = {
        "id": "1.1",
        "title": "光是什么",
        "description": "介绍光的基本概念",
        "learning_objectives": ["理解光的基本特性", "观察光的现象"],
        "key_points": ["光的来源", "光的传播"]
    }
    
    # 构造测试用的章节设计指导（模拟CoursePlanner的输出）
    chapter_info = {
        "title": "第一章：认识光",
        "content_design_guidance": {
            "content_structure": "生活观察→概念介绍→简单实验→总结巩固",
            "difficulty_level": "小学初级",
            "examples_needed": "生活中的光源实例",
            "practice_activities": "观察实验、简单制作"
        },
        "teaching_resources": {
            "interactive_activities": ["观察实验", "小组讨论"],
            "media_types": ["图片", "简单视频"], 
            "assessment_methods": ["问答", "观察记录"]
        }
    }
    
    try:
        result = await content_designer.create_content(
            section_info=section_info,
            course_topic="光的传播",
            user_background=TEST_USER_BACKGROUND,
            chapter_info=chapter_info
        )
        
        print(f"📤 ContentDesigner返回结果类型: {type(result)}")
        
        if isinstance(result, dict):
            print(f"📋 内容标题: {result.get('title', '未找到')}")
            
            # 检查主要内容是否适合小学4年级
            main_content = result.get('mainContent', '')
            if main_content:
                content_preview = main_content[:200] + "..." if len(main_content) > 200 else main_content
                print(f"📄 内容预览: {content_preview}")
                
                # 检查内容是否包含适合小学生的语言和示例
                grade_friendly_indicators = ['小朋友', '同学们', '我们可以', '观察', '发现', '有趣', '身边', '日常']
                found_indicators = [indicator for indicator in grade_friendly_indicators if indicator in main_content]
                
                if found_indicators:
                    print(f"✅ 内容语言适合小学生，包含: {found_indicators}")
                else:
                    print("⚠️  内容可能过于专业，缺少小学生友好的语言")
            
            # 检查关键点是否适合年龄
            key_points = result.get('keyPoints', [])
            if key_points:
                print(f"🔑 关键点: {key_points}")
                
                # 验证关键点是否过于复杂
                complex_terms = ['波长', '频率', '电磁', '量子', '折射率', '光谱分析']
                found_complex = [term for term in complex_terms if any(term in point for point in key_points)]
                
                if found_complex:
                    print(f"⚠️  发现可能过于复杂的概念: {found_complex}")
                else:
                    print("✅ 关键点难度适合小学4年级")
            
            # 检查活动是否适合短时集中学习偏好
            activities = result.get('activities', [])
            if activities:
                print(f"🎯 活动数量: {len(activities)}")
                for i, activity in enumerate(activities):
                    print(f"   活动{i+1}: {activity.get('title', '未命名')} - {activity.get('type', '未知类型')}")
                    
                    # 检查活动描述中是否体现了短时集中的特点
                    description = activity.get('description', '')
                    if any(keyword in description for keyword in ['快速', '短时', '集中', '简单', '观察']):
                        print(f"   ✅ 活动{i+1}适合短时集中学习")
                    else:
                        print(f"   ⚠️  活动{i+1}可能不够体现短时集中特点")
            
            print("✅ ContentDesigner基本使用了用户背景信息")
            return True
        else:
            print(f"❌ ContentDesigner返回了非字典类型: {type(result)}")
            return False
            
    except Exception as e:
        print(f"❌ ContentDesigner调用失败: {e}")
        return False

async def test_full_agent_service_flow():
    """测试完整的agent_service消息处理流程"""
    print("\n🔄 测试4: 完整的AgentService流程")
    print("=" * 50)
    
    client_id = "test_client_123"
    
    try:
        # 模拟完整的消息处理流程
        response = await agent_service.process_message(client_id, TEST_FORMATTED_MESSAGE)
        
        print(f"📤 AgentService完整响应类型: {type(response)}")
        
        if isinstance(response, dict):
            print(f"💬 响应内容预览: {response.get('content', '')[:150]}...")
            
            # 检查是否包含用户背景信息
            if 'user_background' in response:
                returned_background = response['user_background']
                print(f"📊 返回的用户背景: {json.dumps(returned_background, ensure_ascii=False, indent=2)}")
                
                # 验证返回的背景信息是否与输入一致
                for key, expected_value in TEST_USER_BACKGROUND.items():
                    if key in returned_background:
                        actual_value = returned_background[key]
                        if actual_value == expected_value:
                            print(f"✅ {key}: 匹配 ({actual_value})")
                        else:
                            print(f"❌ {key}: 不匹配，期望 '{expected_value}'，实际 '{actual_value}'")
                            return False
                    else:
                        print(f"❌ 缺少背景信息字段: {key}")
                        return False
            else:
                print("❌ 响应中缺少user_background字段")
                return False
            
            # 检查是否包含课程数据
            if 'course_data' in response:
                course_data = response['course_data']
                print(f"📚 课程数据包含字段: {list(course_data.keys())}")
                
                # 验证课程数据是否体现了用户背景
                if 'background_analysis' in course_data:
                    bg_analysis = course_data['background_analysis']
                    print(f"📋 课程背景分析: {json.dumps(bg_analysis, ensure_ascii=False, indent=2)}")
                    
                    if '4年级' in str(bg_analysis) or '小学' in str(bg_analysis):
                        print("✅ 课程数据正确体现了用户年龄背景")
                    else:
                        print("⚠️  课程数据可能未充分体现用户年龄背景")
                else:
                    print("⚠️  课程数据缺少background_analysis字段")
                    
            print("✅ AgentService完整流程处理正确")
            return True
        else:
            print(f"❌ AgentService返回了非字典类型: {type(response)}")
            return False
            
    except Exception as e:
        print(f"❌ AgentService完整流程测试失败: {e}")
        return False

async def run_all_tests():
    """运行所有测试"""
    print("🚀 开始用户背景信息数据流调试测试")
    print("=" * 60)
    
    tests = [
        ("用户背景信息提取", test_user_background_extraction),
        ("CoursePlanner背景信息使用", test_course_planner_background_usage),
        ("ContentDesigner背景信息使用", test_content_designer_background_usage),
        ("AgentService完整流程", test_full_agent_service_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ 测试 '{test_name}' 执行失败: {e}")
            results.append((test_name, False))
    
    print("\n📊 测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
        if success:
            success_count += 1
    
    print(f"\n总体结果: {success_count}/{len(results)} 测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试通过！用户背景信息数据流工作正常。")
    else:
        print("⚠️  存在问题需要修复！")
        
        # 提供修复建议
        print("\n🔧 修复建议:")
        for test_name, success in results:
            if not success:
                if "提取" in test_name:
                    print("- 检查 agent_service._extract_user_background() 方法的解析逻辑")
                elif "CoursePlanner" in test_name:
                    print("- 检查 CoursePlanner Agent 的背景信息处理和模板生成")
                elif "ContentDesigner" in test_name:
                    print("- 检查 ContentDesigner Agent 的个性化内容生成逻辑")
                elif "完整流程" in test_name:
                    print("- 检查 AgentService.process_message() 的消息处理和响应格式")

if __name__ == "__main__":
    asyncio.run(run_all_tests())
