<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程创建流程测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #f9f9f9;
        }
        .step {
            margin: 10px 0;
            padding: 15px;
            background: white;
            border-left: 4px solid #007bff;
            border-radius: 4px;
        }
        .problem {
            border-left-color: #dc3545;
            background: #fff5f5;
        }
        .fixed {
            border-left-color: #28a745;
            background: #f0fff4;
        }
        .test-button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .status {
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; }
        .status.warning { background: #fff3cd; color: #856404; }
        .status.error { background: #f8d7da; color: #721c24; }
        .status.info { background: #d1ecf1; color: #0c5460; }
        
        code {
            background: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: 'Monaco', 'Consolas', monospace;
        }
        .critical {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
        ul.checklist {
            list-style: none;
            padding-left: 0;
        }
        ul.checklist li:before {
            content: "☑️ ";
            margin-right: 8px;
        }
        .arrow {
            color: #007bff;
            font-weight: bold;
            text-align: center;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🛠️ 课程创建新课程流程测试</h1>
    
    <div class="critical">
        <strong>⚠️ 测试目标:</strong> 验证当用户点击"创建新课程"时，页面左侧显示的是占位符等待状态，而不是已有的类似课程大纲
    </div>

    <div class="test-section">
        <h2>🔍 问题描述</h2>
        <div class="step problem">
            <strong>修复前的问题：</strong>
            <ul>
                <li>用户选择"创建新课程"后，页面左侧立即显示已有的类似课程大纲</li>
                <li>而不是显示loading状态等待Course Planner Agent生成新大纲</li>
                <li>这让用户误以为看到的就是新生成的课程</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔧 修复方案</h2>
        <div class="step fixed">
            <strong>关键修复点：</strong>
            <ul class="checklist">
                <li>在<code>handleCreateNewCourse</code>中立即清除现有课程状态</li>
                <li>设置<code>setOutline(null)</code>确保左侧显示空状态</li>
                <li>改进<code>initializePage</code>逻辑防止缓存干扰</li>
                <li>增强状态保护机制防止竞态条件</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🧪 测试步骤</h2>
        
        <div class="step">
            <strong>步骤 1: 准备测试环境</strong>
            <ol>
                <li>确保前端服务运行在 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
                <li>确保后端服务运行在 <a href="http://localhost:8000" target="_blank">http://localhost:8000</a></li>
                <li>清除浏览器localStorage避免缓存干扰</li>
            </ol>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <strong>步骤 2: 测试新课程创建流程</strong>
            <ol>
                <li>访问首页输入一个<strong>全新的主题</strong>（如"量子计算入门"）</li>
                <li>完成背景信息问卷</li>
                <li>点击"生成课程"按钮</li>
                <li>在课程选择对话框中选择"<strong>创建新课程</strong>"</li>
                <li><strong>关键验证点：</strong>页面左侧应该显示空状态或loading，而不是已有课程大纲</li>
            </ol>
        </div>
        
        <div class="arrow">↓</div>
        
        <div class="step">
            <strong>步骤 3: 测试已有课程主题</strong>
            <ol>
                <li>访问首页输入一个<strong>已有的主题</strong>（如"Python编程基础"）</li>
                <li>完成背景信息问卷</li>
                <li>点击"生成课程"按钮</li>
                <li>在课程选择对话框中选择"<strong>创建新课程</strong>"</li>
                <li><strong>关键验证点：</strong>即使是已有主题，页面左侧也应该显示空状态等待新生成</li>
            </ol>
        </div>
    </div>

    <div class="test-section">
        <h2>✅ 预期结果</h2>
        <div class="step fixed">
            <strong>修复后的正确行为：</strong>
            <ul class="checklist">
                <li>用户选择"创建新课程"后，左侧立即清空</li>
                <li>显示"正在为您生成全新的课程大纲..."提示</li>
                <li>页面停留在CoursePlanning等待API响应</li>
                <li>生成完成后显示全新的课程大纲</li>
                <li>课程来源显示为"✨ 新生成"</li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🔍 调试检查点</h2>
        <div class="step">
            <strong>浏览器控制台应该显示：</strong>
            <ul>
                <li><code>=== handleCreateNewCourse started ===</code></li>
                <li><code>Creating new course for: [您的主题]</code></li>
                <li><code>=== handleCreateNewCourse completed successfully ===</code></li>
                <li><strong>不应该出现：</strong><code>Found cached course, loading directly</code></li>
            </ul>
        </div>
    </div>

    <div class="test-section">
        <h2>🚀 快速测试</h2>
        <a href="http://localhost:8080" class="test-button" target="_blank">🏠 开始测试 - 访问首页</a>
        <button class="test-button" onclick="clearStorage()">🗑️ 清除localStorage</button>
        <button class="test-button" onclick="checkAPI()">🔍 检查API状态</button>
        
        <div id="testResults" style="margin-top: 15px;"></div>
    </div>

    <script>
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            document.getElementById('testResults').innerHTML = 
                '<div class="status success">✅ localStorage和sessionStorage已清除</div>';
        }

        async function checkAPI() {
            const results = document.getElementById('testResults');
            results.innerHTML = '<div class="status info">🔍 检查API状态中...</div>';
            
            try {
                // 检查前端
                const frontendResponse = await fetch('http://localhost:8080');
                const frontendOk = frontendResponse.ok;
                
                // 检查后端
                const backendResponse = await fetch('http://localhost:8000/api/course/list');
                const backendData = await backendResponse.json();
                const backendOk = backendResponse.ok;
                
                if (frontendOk && backendOk) {
                    results.innerHTML = `
                        <div class="status success">✅ 前端服务正常</div>
                        <div class="status success">✅ 后端服务正常 (${backendData.courses?.length || 0}个课程)</div>
                        <div class="status info">准备开始测试！</div>
                    `;
                } else {
                    results.innerHTML = `
                        <div class="status ${frontendOk ? 'success' : 'error'}">${frontendOk ? '✅' : '❌'} 前端服务</div>
                        <div class="status ${backendOk ? 'success' : 'error'}">${backendOk ? '✅' : '❌'} 后端服务</div>
                    `;
                }
            } catch (error) {
                results.innerHTML = '<div class="status error">❌ API检查失败: ' + error.message + '</div>';
            }
        }

        // 页面加载时自动检查API状态
        window.onload = function() {
            checkAPI();
        };
    </script>
</body>
</html>
