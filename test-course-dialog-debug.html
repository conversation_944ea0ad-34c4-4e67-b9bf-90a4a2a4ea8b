<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程对话框调试测试</title>
</head>
<body>
    <h1>课程对话框调试测试</h1>
    <p>这个页面用于测试课程对话框的行为</p>
    
    <div>
        <h2>测试步骤：</h2>
        <ol>
            <li>打开浏览器开发者工具控制台</li>
            <li>访问 <a href="http://localhost:8080" target="_blank">http://localhost:8080</a></li>
            <li>在首页输入一个学习主题（例如："机器学习基础"）</li>
            <li>完成背景信息问卷</li>
            <li>观察跳转到课程规划页面后的行为</li>
            <li>在课程选择对话框中点击"生成新课程"</li>
            <li>观察是否有重定向到首页的问题</li>
        </ol>
    </div>
    
    <div>
        <h2>调试要点：</h2>
        <ul>
            <li>检查控制台日志中的 "=== handleCreateNewCourse started ===" 消息</li>
            <li>检查控制台日志中的 "=== CoursePlanning component mounted/updated ===" 消息</li>
            <li>观察页面URL是否发生了意外变化</li>
            <li>检查是否有 PopState 事件触发</li>
        </ul>
    </div>
    
    <div>
        <h2>预期行为：</h2>
        <ul>
            <li>用户在课程选择对话框中点击"生成新课程"后</li>
            <li>对话框应该关闭</li>
            <li>页面应该保持在 /course-planning 路由</li>
            <li>显示"正在为您生成全新的课程大纲..."提示</li>
            <li>生成完成后显示课程大纲</li>
            <li>不应该重定向回首页</li>
        </ul>
    </div>
</body>
</html>
