# CoursePlanning 页面课程加载优化

## 问题描述
原有的CoursePlanning页面存在以下问题：
1. 缓存的课程只显示课程概览，无法正确加载和显示课程章节内容
2. 新生成的课程和缓存课程没有明确区分
3. 用户体验不佳，无法清楚了解课程内容的加载状态

## 解决方案

### 1. 改进课程加载逻辑
- **缓存优先策略**：首先检查localStorage缓存，如果存在则立即加载
- **服务器备份**：如果本地无缓存，检查服务器端是否有存储的课程
- **新生成**：只有在前两者都没有时才生成新课程
- **状态同步**：确保所有加载路径都正确设置`setHasCourseGenerated(true)`

### 2. 用户界面优化

#### 课程源显示
- 添加彩色标签区分不同来源的课程：
  - 📱 本地缓存 (绿色)
  - 🧠 服务器记忆 (紫色) 
  - 📄 文件存储 (蓝色)
  - ✨ 新生成 (橙色)
  - ⚠️ 默认模板 (灰色)

#### 章节状态指示器
- 为每个章节添加缓存状态图标 💾
- 显示课程统计信息（已缓存章节数/总章节数）

#### 加载状态改进
- 区分课程大纲加载和章节内容加载
- 添加更清晰的加载提示信息
- 改进空状态显示，根据是否有课程大纲显示不同内容

### 3. 新增功能

#### 缓存检查函数
```typescript
const checkSectionCached = (sectionId: string): boolean => {
  if (!initialPrompt) return false;
  try {
    const storageKey = getSectionStorageKey(initialPrompt, sectionId);
    return localStorage.getItem(storageKey) !== null;
  } catch {
    return false;
  }
};
```

#### 课程统计显示
- 在课程概览中显示缓存进度
- 实时反映已缓存章节数量

#### 智能空状态
- 有课程大纲时：引导用户选择章节开始学习
- 无课程大纲时：显示课程准备中状态

### 4. 用户体验提升

#### Toast 消息优化
- 缓存加载：`已加载缓存的课程大纲`
- 服务器加载：`已加载存储的课程大纲`
- 新生成：`课程大纲生成完成！`
- 章节缓存：`已加载缓存的章节内容`
- 章节生成：`章节内容生成完成！`

#### 视觉改进
- 课程来源标签使用不同颜色
- 章节按钮显示缓存状态
- 改进的空状态设计
- 更清晰的加载指示器

## 技术实现细节

### 状态管理
- `courseSource`: 记录课程来源
- `isLoadingOutline`: 课程大纲加载状态
- `isLoadingContent`: 章节内容加载状态
- `checkSectionCached()`: 检查章节缓存状态

### 缓存策略
1. **课程大纲缓存**: 使用 `course_outline_${topic}` 格式
2. **章节内容缓存**: 使用 `course_section_${topic}_${sectionId}` 格式
3. **缓存元数据**: 包含 `cachedAt`, `topic`, `sectionId` 等信息

### 加载流程
1. 检查localStorage缓存 → 立即加载 + 设置状态
2. 检查服务器存储 → 加载 + 保存到localStorage + 设置状态
3. 生成新课程 → 保存到localStorage + 设置状态
4. 默认章节选择 → 自动选择第一个章节

## 预期效果

### 用户体验
- ✅ 缓存课程立即加载，无等待时间
- ✅ 清晰区分课程来源，建立用户信任
- ✅ 直观的章节缓存状态显示
- ✅ 流畅的加载过渡和反馈

### 性能优化
- ✅ 减少不必要的API调用
- ✅ 智能缓存管理
- ✅ 渐进式内容加载

### 维护性
- ✅ 清晰的状态管理
- ✅ 可扩展的缓存策略
- ✅ 统一的错误处理

## 测试建议

1. **缓存测试**：清空localStorage后首次访问，然后刷新页面验证缓存
2. **状态测试**：验证不同来源的课程显示不同的标签
3. **章节测试**：验证章节缓存状态显示和内容加载
4. **空状态测试**：验证没有选择章节时的引导界面
5. **性能测试**：测试缓存命中时的加载速度
